"""
系统门面
提供统一的系统入口，隐藏内部复杂性，简化客户端调用
"""

from typing import Dict, List, Any, Optional, Union
import pandas as pd
from datetime import datetime
from pathlib import Path

from .interfaces import (
    IPredictor, PredictionResult, PredictionType, BallType, 
    LotteryConfig, PredictionContext
)
from .predictor_factory import get_predictor_factory, create_predictor
from .dependency_container import get_container
from ..validation.validators import DataValidator
from ..logging.simple_logger import get_logger


class LotteryPredictionFacade:
    """彩票预测系统门面"""
    
    def __init__(self, config_path: str = None):
        """
        初始化系统门面
        
        Args:
            config_path: 配置文件路径
        """
        self.config = LotteryConfig()
        self.data: Optional[pd.DataFrame] = None
        self.logger = get_logger()
        self.validator = DataValidator()
        self.factory = get_predictor_factory()
        self.container = get_container()
        
        # 预测器缓存
        self._predictors: Dict[str, IPredictor] = {}
        
        # 加载配置
        if config_path:
            self._load_system_config(config_path)
        
        self.logger.info("彩票预测系统门面初始化完成")
    
    def _load_system_config(self, config_path: str) -> None:
        """加载系统配置"""
        try:
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新彩票配置
            if 'lottery_config' in config_data:
                lottery_cfg = config_data['lottery_config']
                self.config = LotteryConfig(
                    red_ball_range=tuple(lottery_cfg.get('red_ball_range', (1, 35))),
                    blue_ball_range=tuple(lottery_cfg.get('blue_ball_range', (1, 12))),
                    red_ball_count=lottery_cfg.get('red_ball_count', 5),
                    blue_ball_count=lottery_cfg.get('blue_ball_count', 2)
                )
            
            self.logger.info(f"系统配置加载成功: {config_path}")
            
        except Exception as e:
            self.logger.warning(f"系统配置加载失败: {e}，使用默认配置")
    
    def load_data(self, data_path: str, encoding: str = 'utf-8') -> bool:
        """
        加载历史数据
        
        Args:
            data_path: 数据文件路径
            encoding: 文件编码
            
        Returns:
            加载是否成功
        """
        try:
            # 尝试多种编码
            encodings = [encoding, 'utf-8', 'gbk', 'gb2312']
            
            for enc in encodings:
                try:
                    self.data = pd.read_csv(data_path, encoding=enc)
                    self.logger.info(f"数据加载成功: {data_path} (编码: {enc})")
                    break
                except UnicodeDecodeError:
                    continue
            
            if self.data is None:
                raise ValueError("所有编码尝试失败")
            
            # 验证数据
            self.validator.validate_lottery_data(self.data, self.config)
            
            # 训练预测器
            self._train_all_predictors()
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            return False
    
    def _train_all_predictors(self) -> None:
        """训练所有预测器"""
        if self.data is None:
            raise ValueError("数据未加载")
        
        # 获取所有可用预测器
        available_predictors = self.factory.get_available_predictors()
        
        for predictor_name in available_predictors:
            try:
                predictor = self._get_predictor(predictor_name)
                predictor.train(self.data)
                self.logger.info(f"预测器训练完成: {predictor_name}")
                
            except Exception as e:
                self.logger.warning(f"预测器训练失败 {predictor_name}: {e}")
    
    def _get_predictor(self, predictor_name: str) -> IPredictor:
        """获取预测器实例（带缓存）"""
        if predictor_name not in self._predictors:
            self._predictors[predictor_name] = create_predictor(predictor_name)
        
        return self._predictors[predictor_name]
    
    def predict_numbers(self, ball_type: Union[BallType, str], 
                       predictor_name: str = None, 
                       current_period: str = None) -> PredictionResult:
        """
        预测号码
        
        Args:
            ball_type: 球类型
            predictor_name: 预测器名称，为None时使用默认预测器
            current_period: 当前期号
            
        Returns:
            预测结果
        """
        if self.data is None:
            raise ValueError("数据未加载，请先调用 load_data()")
        
        # 选择预测器
        if predictor_name is None:
            # 使用默认的号码预测器
            number_predictors = self.factory.get_predictors_by_type('number')
            if not number_predictors:
                raise ValueError("未找到可用的号码预测器")
            predictor_name = number_predictors[0]
        
        predictor = self._get_predictor(predictor_name)
        
        # 创建预测上下文
        context = PredictionContext(
            current_period=current_period or f"预测_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            historical_data=self.data,
            config=self.config
        )
        
        # 执行预测
        result = predictor.predict(
            self.data, 0, PredictionType.NUMBERS, ball_type
        )
        
        self.logger.info(f"号码预测完成: {ball_type} -> {result.predicted_value}")
        return result
    
    def predict_kill_numbers(self, ball_type: Union[BallType, str], 
                           target_count: int = None,
                           predictor_name: str = None) -> PredictionResult:
        """
        预测杀号
        
        Args:
            ball_type: 球类型
            target_count: 杀号数量
            predictor_name: 预测器名称
            
        Returns:
            预测结果
        """
        if self.data is None:
            raise ValueError("数据未加载，请先调用 load_data()")
        
        # 选择预测器
        if predictor_name is None:
            kill_predictors = self.factory.get_predictors_by_type('kill_number')
            if not kill_predictors:
                raise ValueError("未找到可用的杀号预测器")
            predictor_name = kill_predictors[0]
        
        predictor = self._get_predictor(predictor_name)
        
        # 设置默认杀号数量
        if target_count is None:
            if isinstance(ball_type, str):
                ball_type = BallType(ball_type)
            target_count = 13 if ball_type == BallType.RED else 5
        
        # 执行预测
        result = predictor.predict(
            self.data, 0, PredictionType.KILL_NUMBERS, ball_type
        )
        
        self.logger.info(f"杀号预测完成: {ball_type} -> {result.predicted_value}")
        return result
    
    def predict_probabilities(self, ball_type: Union[BallType, str],
                            predictor_name: str = None) -> PredictionResult:
        """
        预测概率分布
        
        Args:
            ball_type: 球类型
            predictor_name: 预测器名称
            
        Returns:
            预测结果
        """
        if self.data is None:
            raise ValueError("数据未加载，请先调用 load_data()")
        
        # 选择预测器
        if predictor_name is None:
            prob_predictors = self.factory.get_predictors_by_type('probability')
            if not prob_predictors:
                raise ValueError("未找到可用的概率预测器")
            predictor_name = prob_predictors[0]
        
        predictor = self._get_predictor(predictor_name)
        
        # 执行预测
        result = predictor.predict(
            self.data, 0, PredictionType.PROBABILITIES, ball_type
        )
        
        self.logger.info(f"概率预测完成: {ball_type}")
        return result
    
    def predict_comprehensive(self, current_period: str = None) -> Dict[str, Any]:
        """
        综合预测（包含号码、杀号、概率）
        
        Args:
            current_period: 当前期号
            
        Returns:
            综合预测结果
        """
        if self.data is None:
            raise ValueError("数据未加载，请先调用 load_data()")
        
        if current_period is None:
            current_period = f"预测_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        results = {
            'period': current_period,
            'timestamp': datetime.now().isoformat(),
            'predictions': {}
        }
        
        try:
            # 红球预测
            results['predictions']['red'] = {
                'numbers': self.predict_numbers(BallType.RED, current_period=current_period),
                'kill_numbers': self.predict_kill_numbers(BallType.RED),
                'probabilities': self.predict_probabilities(BallType.RED)
            }
            
            # 蓝球预测
            results['predictions']['blue'] = {
                'numbers': self.predict_numbers(BallType.BLUE, current_period=current_period),
                'kill_numbers': self.predict_kill_numbers(BallType.BLUE),
                'probabilities': self.predict_probabilities(BallType.BLUE)
            }
            
            self.logger.info(f"综合预测完成: {current_period}")
            
        except Exception as e:
            self.logger.error(f"综合预测失败: {e}")
            results['error'] = str(e)
        
        return results
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            'data_loaded': self.data is not None,
            'data_size': len(self.data) if self.data is not None else 0,
            'available_predictors': self.factory.get_available_predictors(),
            'loaded_predictors': list(self._predictors.keys()),
            'config': {
                'red_ball_range': self.config.red_ball_range,
                'blue_ball_range': self.config.blue_ball_range,
                'red_ball_count': self.config.red_ball_count,
                'blue_ball_count': self.config.blue_ball_count
            }
        }
        
        return status
    
    def save_predictions(self, predictions: Dict[str, Any], 
                        output_path: str = None) -> str:
        """
        保存预测结果
        
        Args:
            predictions: 预测结果
            output_path: 输出路径
            
        Returns:
            保存的文件路径
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"predictions_{timestamp}.json"
        
        try:
            import json
            
            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 转换PredictionResult对象为字典
            serializable_predictions = self._serialize_predictions(predictions)
            
            # 保存预测结果
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_predictions, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"预测结果已保存: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"保存预测结果失败: {e}")
            raise
    
    def _serialize_predictions(self, predictions: Any) -> Any:
        """序列化预测结果"""
        if isinstance(predictions, PredictionResult):
            return {
                'predicted_value': predictions.predicted_value,
                'confidence': predictions.confidence,
                'prediction_type': predictions.prediction_type.value,
                'ball_type': predictions.ball_type.value,
                'metadata': predictions.metadata
            }
        elif isinstance(predictions, dict):
            return {k: self._serialize_predictions(v) for k, v in predictions.items()}
        elif isinstance(predictions, list):
            return [self._serialize_predictions(item) for item in predictions]
        else:
            return predictions


# 全局门面实例
_facade = None


def get_lottery_facade(config_path: str = None) -> LotteryPredictionFacade:
    """获取彩票预测系统门面实例"""
    global _facade
    
    if _facade is None:
        _facade = LotteryPredictionFacade(config_path)
    
    return _facade