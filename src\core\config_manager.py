"""统一配置管理系统

提供配置加载、验证、更新和持久化功能。
"""

import os
import json
import yaml
import toml
import configparser
from typing import Dict, List, Optional, Any, Union, Type, Callable
from dataclasses import dataclass, field, asdict
from enum import Enum
from pathlib import Path
from datetime import datetime
import threading
from functools import wraps
import copy
import hashlib
import tempfile
import shutil

from .exceptions import SystemException, ErrorCode


class ConfigFormat(Enum):
    """配置文件格式"""
    JSON = 'json'
    YAML = 'yaml'
    TOML = 'toml'
    INI = 'ini'
    ENV = 'env'
    PYTHON = 'py'


class ConfigSource(Enum):
    """配置来源"""
    FILE = 'file'
    ENVIRONMENT = 'environment'
    COMMAND_LINE = 'command_line'
    DATABASE = 'database'
    REMOTE = 'remote'
    DEFAULT = 'default'


class ValidationLevel(Enum):
    """验证级别"""
    NONE = 'none'
    BASIC = 'basic'
    STRICT = 'strict'
    CUSTOM = 'custom'


@dataclass
class ConfigSchema:
    """配置模式"""
    name: str
    type: Type
    required: bool = True
    default: Any = None
    description: str = ''
    validator: Optional[Callable[[Any], bool]] = None
    choices: Optional[List[Any]] = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    pattern: Optional[str] = None
    
    def validate(self, value: Any) -> bool:
        """验证值
        
        Args:
            value: 要验证的值
        
        Returns:
            bool: 验证结果
        """
        # 类型检查
        if not isinstance(value, self.type):
            try:
                value = self.type(value)
            except (ValueError, TypeError):
                return False
        
        # 选择检查
        if self.choices and value not in self.choices:
            return False
        
        # 范围检查
        if self.min_value is not None and value < self.min_value:
            return False
        if self.max_value is not None and value > self.max_value:
            return False
        
        # 模式检查
        if self.pattern and isinstance(value, str):
            import re
            if not re.match(self.pattern, value):
                return False
        
        # 自定义验证
        if self.validator and not self.validator(value):
            return False
        
        return True


@dataclass
class ConfigMetadata:
    """配置元数据"""
    source: ConfigSource
    format: ConfigFormat
    file_path: Optional[str] = None
    last_modified: Optional[datetime] = None
    checksum: Optional[str] = None
    version: str = '1.0'
    description: str = ''
    tags: List[str] = field(default_factory=list)
    
    def calculate_checksum(self, data: Dict[str, Any]) -> str:
        """计算配置数据的校验和
        
        Args:
            data: 配置数据
        
        Returns:
            str: 校验和
        """
        content = json.dumps(data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(content.encode('utf-8')).hexdigest()


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    config_name: str
    key: str
    old_value: Any
    new_value: Any
    timestamp: datetime
    source: ConfigSource
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class ConfigLoader:
    """配置加载器"""
    
    def __init__(self):
        self.loaders = {
            ConfigFormat.JSON: self._load_json,
            ConfigFormat.YAML: self._load_yaml,
            ConfigFormat.TOML: self._load_toml,
            ConfigFormat.INI: self._load_ini,
            ConfigFormat.ENV: self._load_env,
            ConfigFormat.PYTHON: self._load_python
        }
        
        self.savers = {
            ConfigFormat.JSON: self._save_json,
            ConfigFormat.YAML: self._save_yaml,
            ConfigFormat.TOML: self._save_toml,
            ConfigFormat.INI: self._save_ini
        }
    
    def load(self, file_path: str, format: Optional[ConfigFormat] = None) -> Dict[str, Any]:
        """加载配置文件
        
        Args:
            file_path: 文件路径
            format: 配置格式
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        path = Path(file_path)
        
        if not path.exists():
            raise SystemException(f"配置文件不存在: {file_path}", ErrorCode.FILE_NOT_FOUND)
        
        # 自动检测格式
        if format is None:
            format = self._detect_format(path)
        
        loader = self.loaders.get(format)
        if not loader:
            raise SystemException(f"不支持的配置格式: {format}", ErrorCode.INVALID_CONFIG)
        
        try:
            return loader(path)
        except Exception as e:
            raise SystemException(f"加载配置文件失败: {e}", ErrorCode.CONFIG_LOAD_ERROR)
    
    def save(self, data: Dict[str, Any], file_path: str, format: Optional[ConfigFormat] = None):
        """保存配置文件
        
        Args:
            data: 配置数据
            file_path: 文件路径
            format: 配置格式
        """
        path = Path(file_path)
        
        # 自动检测格式
        if format is None:
            format = self._detect_format(path)
        
        saver = self.savers.get(format)
        if not saver:
            raise SystemException(f"不支持保存格式: {format}", ErrorCode.INVALID_CONFIG)
        
        # 创建目录
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # 原子写入
        temp_path = path.with_suffix(path.suffix + '.tmp')
        
        try:
            saver(data, temp_path)
            shutil.move(str(temp_path), str(path))
        except Exception as e:
            if temp_path.exists():
                temp_path.unlink()
            raise SystemException(f"保存配置文件失败: {e}", ErrorCode.CONFIG_SAVE_ERROR)
    
    def _detect_format(self, path: Path) -> ConfigFormat:
        """检测配置格式
        
        Args:
            path: 文件路径
        
        Returns:
            ConfigFormat: 配置格式
        """
        suffix = path.suffix.lower()
        
        format_map = {
            '.json': ConfigFormat.JSON,
            '.yaml': ConfigFormat.YAML,
            '.yml': ConfigFormat.YAML,
            '.toml': ConfigFormat.TOML,
            '.ini': ConfigFormat.INI,
            '.cfg': ConfigFormat.INI,
            '.conf': ConfigFormat.INI,
            '.py': ConfigFormat.PYTHON
        }
        
        return format_map.get(suffix, ConfigFormat.JSON)
    
    def _load_json(self, path: Path) -> Dict[str, Any]:
        """加载JSON配置"""
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_yaml(self, path: Path) -> Dict[str, Any]:
        """加载YAML配置"""
        with open(path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    
    def _load_toml(self, path: Path) -> Dict[str, Any]:
        """加载TOML配置"""
        with open(path, 'r', encoding='utf-8') as f:
            return toml.load(f)
    
    def _load_ini(self, path: Path) -> Dict[str, Any]:
        """加载INI配置"""
        config = configparser.ConfigParser()
        config.read(path, encoding='utf-8')
        
        result = {}
        for section_name in config.sections():
            section = {}
            for key, value in config.items(section_name):
                # 尝试转换类型
                section[key] = self._convert_value(value)
            result[section_name] = section
        
        return result
    
    def _load_env(self, path: Path) -> Dict[str, Any]:
        """加载环境变量配置"""
        result = {}
        
        with open(path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        result[key.strip()] = self._convert_value(value.strip())
        
        return result
    
    def _load_python(self, path: Path) -> Dict[str, Any]:
        """加载Python配置"""
        import importlib.util
        
        spec = importlib.util.spec_from_file_location("config", path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 提取配置变量
        result = {}
        for name in dir(module):
            if not name.startswith('_'):
                value = getattr(module, name)
                if not callable(value) and not isinstance(value, type):
                    result[name] = value
        
        return result
    
    def _save_json(self, data: Dict[str, Any], path: Path):
        """保存JSON配置"""
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def _save_yaml(self, data: Dict[str, Any], path: Path):
        """保存YAML配置"""
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
    
    def _save_toml(self, data: Dict[str, Any], path: Path):
        """保存TOML配置"""
        with open(path, 'w', encoding='utf-8') as f:
            toml.dump(data, f)
    
    def _save_ini(self, data: Dict[str, Any], path: Path):
        """保存INI配置"""
        config = configparser.ConfigParser()
        
        for section_name, section_data in data.items():
            if isinstance(section_data, dict):
                config.add_section(section_name)
                for key, value in section_data.items():
                    config.set(section_name, key, str(value))
        
        with open(path, 'w', encoding='utf-8') as f:
            config.write(f)
    
    def _convert_value(self, value: str) -> Any:
        """转换字符串值为适当的类型
        
        Args:
            value: 字符串值
        
        Returns:
            Any: 转换后的值
        """
        # 移除引号
        value = value.strip('"\'')
        
        # 布尔值
        if value.lower() in ('true', 'yes', 'on', '1'):
            return True
        elif value.lower() in ('false', 'no', 'off', '0'):
            return False
        
        # 数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 字符串
        return value


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.schemas: Dict[str, ConfigSchema] = {}
    
    def add_schema(self, schema: ConfigSchema):
        """添加配置模式
        
        Args:
            schema: 配置模式
        """
        self.schemas[schema.name] = schema
    
    def validate(self, config: Dict[str, Any], level: ValidationLevel = ValidationLevel.BASIC) -> List[str]:
        """验证配置
        
        Args:
            config: 配置数据
            level: 验证级别
        
        Returns:
            List[str]: 错误信息列表
        """
        errors = []
        
        if level == ValidationLevel.NONE:
            return errors
        
        # 检查必需字段
        for name, schema in self.schemas.items():
            if schema.required and name not in config:
                errors.append(f"缺少必需配置项: {name}")
                continue
            
            if name in config:
                value = config[name]
                if not schema.validate(value):
                    errors.append(f"配置项 {name} 验证失败: {value}")
        
        # 检查未知字段（严格模式）
        if level == ValidationLevel.STRICT:
            for name in config:
                if name not in self.schemas:
                    errors.append(f"未知配置项: {name}")
        
        return errors
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置
        
        Returns:
            Dict[str, Any]: 默认配置
        """
        config = {}
        
        for name, schema in self.schemas.items():
            if schema.default is not None:
                config[name] = schema.default
        
        return config


class ConfigWatcher:
    """配置文件监控器"""
    
    def __init__(self, callback: Callable[[str], None]):
        self.callback = callback
        self.watched_files: Dict[str, float] = {}
        self.running = False
        self.thread: Optional[threading.Thread] = None
        self.check_interval = 1.0
    
    def watch(self, file_path: str):
        """监控文件
        
        Args:
            file_path: 文件路径
        """
        path = Path(file_path)
        if path.exists():
            self.watched_files[file_path] = path.stat().st_mtime
    
    def unwatch(self, file_path: str):
        """停止监控文件
        
        Args:
            file_path: 文件路径
        """
        self.watched_files.pop(file_path, None)
    
    def start(self):
        """开始监控"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._watch_loop, daemon=True)
            self.thread.start()
    
    def stop(self):
        """停止监控"""
        self.running = False
        if self.thread:
            self.thread.join()
    
    def _watch_loop(self):
        """监控循环"""
        import time
        
        while self.running:
            try:
                for file_path, last_mtime in list(self.watched_files.items()):
                    path = Path(file_path)
                    if path.exists():
                        current_mtime = path.stat().st_mtime
                        if current_mtime > last_mtime:
                            self.watched_files[file_path] = current_mtime
                            self.callback(file_path)
                    else:
                        # 文件被删除
                        del self.watched_files[file_path]
                
                time.sleep(self.check_interval)
            
            except Exception:
                # 忽略监控错误
                pass


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.configs: Dict[str, Dict[str, Any]] = {}
        self.metadata: Dict[str, ConfigMetadata] = {}
        self.schemas: Dict[str, Dict[str, ConfigSchema]] = {}
        self.loader = ConfigLoader()
        self.validator = ConfigValidator()
        self.watchers: Dict[str, ConfigWatcher] = {}
        self.change_listeners: List[Callable[[ConfigChangeEvent], None]] = []
        self._lock = threading.RLock()
        
        # 环境变量前缀
        self.env_prefix = 'APP_'
    
    def register_schema(self, config_name: str, schemas: List[ConfigSchema]):
        """注册配置模式
        
        Args:
            config_name: 配置名称
            schemas: 配置模式列表
        """
        with self._lock:
            self.schemas[config_name] = {schema.name: schema for schema in schemas}
    
    def load_config(self, config_name: str, file_path: str, 
                   format: Optional[ConfigFormat] = None,
                   validation_level: ValidationLevel = ValidationLevel.BASIC,
                   watch: bool = False) -> Dict[str, Any]:
        """加载配置
        
        Args:
            config_name: 配置名称
            file_path: 文件路径
            format: 配置格式
            validation_level: 验证级别
            watch: 是否监控文件变化
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        with self._lock:
            # 加载配置文件
            config_data = self.loader.load(file_path, format)
            
            # 合并环境变量
            env_config = self._load_env_config(config_name)
            config_data.update(env_config)
            
            # 验证配置
            if config_name in self.schemas:
                validator = ConfigValidator()
                for schema in self.schemas[config_name].values():
                    validator.add_schema(schema)
                
                errors = validator.validate(config_data, validation_level)
                if errors:
                    raise SystemException(f"配置验证失败: {', '.join(errors)}", ErrorCode.INVALID_CONFIG)
            
            # 保存配置
            self.configs[config_name] = config_data
            
            # 创建元数据
            path = Path(file_path)
            metadata = ConfigMetadata(
                source=ConfigSource.FILE,
                format=format or self.loader._detect_format(path),
                file_path=file_path,
                last_modified=datetime.fromtimestamp(path.stat().st_mtime),
                version='1.0'
            )
            metadata.checksum = metadata.calculate_checksum(config_data)
            self.metadata[config_name] = metadata
            
            # 监控文件变化
            if watch:
                self._watch_config(config_name, file_path)
            
            return config_data.copy()
    
    def save_config(self, config_name: str, file_path: Optional[str] = None,
                   format: Optional[ConfigFormat] = None):
        """保存配置
        
        Args:
            config_name: 配置名称
            file_path: 文件路径
            format: 配置格式
        """
        with self._lock:
            if config_name not in self.configs:
                raise SystemException(f"配置不存在: {config_name}", ErrorCode.CONFIG_NOT_FOUND)
            
            # 使用原文件路径或指定路径
            if file_path is None:
                metadata = self.metadata.get(config_name)
                if not metadata or not metadata.file_path:
                    raise SystemException(f"无法确定配置文件路径: {config_name}", ErrorCode.INVALID_CONFIG)
                file_path = metadata.file_path
            
            # 保存配置
            config_data = self.configs[config_name]
            self.loader.save(config_data, file_path, format)
            
            # 更新元数据
            if config_name in self.metadata:
                metadata = self.metadata[config_name]
                metadata.last_modified = datetime.now()
                metadata.checksum = metadata.calculate_checksum(config_data)
    
    def get_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取配置
        
        Args:
            config_name: 配置名称
        
        Returns:
            Optional[Dict[str, Any]]: 配置数据
        """
        with self._lock:
            return self.configs.get(config_name, {}).copy() if config_name in self.configs else None
    
    def get_value(self, config_name: str, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            config_name: 配置名称
            key: 配置键（支持点号分隔的嵌套键）
            default: 默认值
        
        Returns:
            Any: 配置值
        """
        with self._lock:
            config = self.configs.get(config_name, {})
            
            # 支持嵌套键
            keys = key.split('.')
            value = config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
    
    def set_value(self, config_name: str, key: str, value: Any, 
                 source: ConfigSource = ConfigSource.DEFAULT):
        """设置配置值
        
        Args:
            config_name: 配置名称
            key: 配置键
            value: 配置值
            source: 配置来源
        """
        with self._lock:
            if config_name not in self.configs:
                self.configs[config_name] = {}
            
            config = self.configs[config_name]
            old_value = config.get(key)
            
            # 支持嵌套键
            keys = key.split('.')
            current = config
            
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            
            current[keys[-1]] = value
            
            # 触发变更事件
            event = ConfigChangeEvent(
                config_name=config_name,
                key=key,
                old_value=old_value,
                new_value=value,
                timestamp=datetime.now(),
                source=source
            )
            
            self._notify_change(event)
    
    def reload_config(self, config_name: str):
        """重新加载配置
        
        Args:
            config_name: 配置名称
        """
        with self._lock:
            metadata = self.metadata.get(config_name)
            if not metadata or not metadata.file_path:
                raise SystemException(f"无法重新加载配置: {config_name}", ErrorCode.CONFIG_NOT_FOUND)
            
            # 重新加载
            self.load_config(
                config_name,
                metadata.file_path,
                metadata.format,
                ValidationLevel.BASIC,
                config_name in self.watchers
            )
    
    def add_change_listener(self, listener: Callable[[ConfigChangeEvent], None]):
        """添加变更监听器
        
        Args:
            listener: 监听器函数
        """
        self.change_listeners.append(listener)
    
    def remove_change_listener(self, listener: Callable[[ConfigChangeEvent], None]):
        """移除变更监听器
        
        Args:
            listener: 监听器函数
        """
        if listener in self.change_listeners:
            self.change_listeners.remove(listener)
    
    def _load_env_config(self, config_name: str) -> Dict[str, Any]:
        """加载环境变量配置
        
        Args:
            config_name: 配置名称
        
        Returns:
            Dict[str, Any]: 环境变量配置
        """
        env_config = {}
        prefix = f"{self.env_prefix}{config_name.upper()}_"
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower()
                env_config[config_key] = self.loader._convert_value(value)
        
        return env_config
    
    def _watch_config(self, config_name: str, file_path: str):
        """监控配置文件
        
        Args:
            config_name: 配置名称
            file_path: 文件路径
        """
        def on_change(changed_file: str):
            try:
                self.reload_config(config_name)
            except Exception:
                # 忽略重新加载错误
                pass
        
        watcher = ConfigWatcher(on_change)
        watcher.watch(file_path)
        watcher.start()
        
        self.watchers[config_name] = watcher
    
    def _notify_change(self, event: ConfigChangeEvent):
        """通知配置变更
        
        Args:
            event: 变更事件
        """
        for listener in self.change_listeners:
            try:
                listener(event)
            except Exception:
                # 忽略监听器错误
                pass
    
    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有配置
        """
        with self._lock:
            return {name: config.copy() for name, config in self.configs.items()}
    
    def get_metadata(self, config_name: str) -> Optional[ConfigMetadata]:
        """获取配置元数据
        
        Args:
            config_name: 配置名称
        
        Returns:
            Optional[ConfigMetadata]: 配置元数据
        """
        return self.metadata.get(config_name)
    
    def close(self):
        """关闭配置管理器"""
        with self._lock:
            # 停止所有监控器
            for watcher in self.watchers.values():
                watcher.stop()
            
            self.watchers.clear()
            self.change_listeners.clear()


# 全局配置管理器实例
_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取配置管理器实例
    
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def load_config(config_name: str, file_path: str, **kwargs) -> Dict[str, Any]:
    """加载配置的便捷函数
    
    Args:
        config_name: 配置名称
        file_path: 文件路径
        **kwargs: 其他参数
    
    Returns:
        Dict[str, Any]: 配置数据
    """
    return get_config_manager().load_config(config_name, file_path, **kwargs)


def get_config(config_name: str) -> Optional[Dict[str, Any]]:
    """获取配置的便捷函数
    
    Args:
        config_name: 配置名称
    
    Returns:
        Optional[Dict[str, Any]]: 配置数据
    """
    return get_config_manager().get_config(config_name)


def get_config_value(config_name: str, key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数
    
    Args:
        config_name: 配置名称
        key: 配置键
        default: 默认值
    
    Returns:
        Any: 配置值
    """
    return get_config_manager().get_value(config_name, key, default)


def set_config_value(config_name: str, key: str, value: Any):
    """设置配置值的便捷函数
    
    Args:
        config_name: 配置名称
        key: 配置键
        value: 配置值
    """
    get_config_manager().set_value(config_name, key, value)


# 配置装饰器
def config_required(config_name: str, *required_keys: str):
    """配置必需装饰器
    
    Args:
        config_name: 配置名称
        *required_keys: 必需的配置键
    
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            manager = get_config_manager()
            config = manager.get_config(config_name)
            
            if not config:
                raise SystemException(f"配置不存在: {config_name}", ErrorCode.CONFIG_NOT_FOUND)
            
            # 检查必需键
            for key in required_keys:
                if manager.get_value(config_name, key) is None:
                    raise SystemException(f"缺少必需配置: {config_name}.{key}", ErrorCode.INVALID_CONFIG)
            
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


# 导出的公共接口
__all__ = [
    'ConfigFormat',
    'ConfigSource',
    'ValidationLevel',
    'ConfigSchema',
    'ConfigMetadata',
    'ConfigChangeEvent',
    'ConfigLoader',
    'ConfigValidator',
    'ConfigWatcher',
    'ConfigManager',
    'get_config_manager',
    'load_config',
    'get_config',
    'get_config_value',
    'set_config_value',
    'config_required'
]