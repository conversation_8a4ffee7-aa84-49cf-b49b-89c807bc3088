"""统一API网关系统

提供API路由、认证、限流、监控和文档生成功能。
"""

import time
import json
import hashlib
import hmac
import jwt
import asyncio
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from functools import wraps
from collections import defaultdict, deque
import threading
from pathlib import Path
import inspect
from urllib.parse import parse_qs, urlparse

from .exceptions import NetworkException, SystemException, ErrorCode
from .logging_manager import get_logger, log_performance
from .cache_manager import get_cache_manager
from .performance_monitor import get_performance_monitor, record_metric


class HTTPMethod(Enum):
    """HTTP方法"""
    GET = 'GET'
    POST = 'POST'
    PUT = 'PUT'
    DELETE = 'DELETE'
    PATCH = 'PATCH'
    HEAD = 'HEAD'
    OPTIONS = 'OPTIONS'


class AuthType(Enum):
    """认证类型"""
    NONE = 'none'  # 无认证
    API_KEY = 'api_key'  # API密钥
    JWT = 'jwt'  # JWT令牌
    BASIC = 'basic'  # 基础认证
    OAUTH2 = 'oauth2'  # OAuth2
    CUSTOM = 'custom'  # 自定义认证


class RateLimitType(Enum):
    """限流类型"""
    REQUESTS_PER_SECOND = 'rps'  # 每秒请求数
    REQUESTS_PER_MINUTE = 'rpm'  # 每分钟请求数
    REQUESTS_PER_HOUR = 'rph'  # 每小时请求数
    REQUESTS_PER_DAY = 'rpd'  # 每天请求数
    CONCURRENT_REQUESTS = 'concurrent'  # 并发请求数


class ResponseFormat(Enum):
    """响应格式"""
    JSON = 'json'
    XML = 'xml'
    HTML = 'html'
    TEXT = 'text'
    BINARY = 'binary'


@dataclass
class APIRequest:
    """API请求"""
    method: HTTPMethod
    path: str
    headers: Dict[str, str] = field(default_factory=dict)
    query_params: Dict[str, Any] = field(default_factory=dict)
    body: Any = None
    client_ip: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    request_id: Optional[str] = None
    
    # 认证信息
    auth_token: Optional[str] = None
    user_id: Optional[str] = None
    api_key: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class APIResponse:
    """API响应"""
    status_code: int
    headers: Dict[str, str] = field(default_factory=dict)
    body: Any = None
    format: ResponseFormat = ResponseFormat.JSON
    
    # 性能信息
    processing_time: Optional[float] = None
    cache_hit: bool = False
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'status_code': self.status_code,
            'headers': self.headers,
            'body': self.body,
            'format': self.format.value,
            'processing_time': self.processing_time,
            'cache_hit': self.cache_hit,
            'metadata': self.metadata
        }


@dataclass
class RateLimitRule:
    """限流规则"""
    type: RateLimitType
    limit: int
    window_size: int = 60  # 时间窗口大小（秒）
    burst_limit: Optional[int] = None  # 突发限制
    
    # 应用范围
    apply_to_ip: bool = True
    apply_to_user: bool = True
    apply_to_api_key: bool = True
    
    # 白名单
    whitelist_ips: List[str] = field(default_factory=list)
    whitelist_users: List[str] = field(default_factory=list)
    whitelist_api_keys: List[str] = field(default_factory=list)


@dataclass
class APIEndpoint:
    """API端点"""
    path: str
    method: HTTPMethod
    handler: Callable
    
    # 认证配置
    auth_type: AuthType = AuthType.NONE
    auth_config: Dict[str, Any] = field(default_factory=dict)
    
    # 限流配置
    rate_limits: List[RateLimitRule] = field(default_factory=list)
    
    # 缓存配置
    cache_enabled: bool = False
    cache_ttl: int = 300  # 缓存TTL（秒）
    cache_key_generator: Optional[Callable] = None
    
    # 文档配置
    description: str = ""
    tags: List[str] = field(default_factory=list)
    parameters: List[Dict[str, Any]] = field(default_factory=list)
    responses: Dict[int, Dict[str, Any]] = field(default_factory=dict)
    
    # 中间件
    middlewares: List[Callable] = field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)


class RateLimiter:
    """限流器"""
    
    def __init__(self):
        self.counters: Dict[str, deque] = defaultdict(deque)
        self.concurrent_counters: Dict[str, int] = defaultdict(int)
        self._lock = threading.RLock()
    
    def is_allowed(self, key: str, rule: RateLimitRule) -> Tuple[bool, Dict[str, Any]]:
        """检查是否允许请求
        
        Args:
            key: 限流键
            rule: 限流规则
        
        Returns:
            Tuple[bool, Dict[str, Any]]: (是否允许, 限流信息)
        """
        with self._lock:
            current_time = time.time()
            
            if rule.type == RateLimitType.CONCURRENT_REQUESTS:
                return self._check_concurrent_limit(key, rule)
            else:
                return self._check_rate_limit(key, rule, current_time)
    
    def _check_rate_limit(self, key: str, rule: RateLimitRule, current_time: float) -> Tuple[bool, Dict[str, Any]]:
        """检查速率限制"""
        counter = self.counters[key]
        
        # 清理过期的请求记录
        window_start = current_time - rule.window_size
        while counter and counter[0] < window_start:
            counter.popleft()
        
        # 检查是否超过限制
        current_count = len(counter)
        
        info = {
            'limit': rule.limit,
            'remaining': max(0, rule.limit - current_count),
            'reset_time': window_start + rule.window_size,
            'window_size': rule.window_size
        }
        
        if current_count >= rule.limit:
            return False, info
        
        # 记录当前请求
        counter.append(current_time)
        info['remaining'] -= 1
        
        return True, info
    
    def _check_concurrent_limit(self, key: str, rule: RateLimitRule) -> Tuple[bool, Dict[str, Any]]:
        """检查并发限制"""
        current_count = self.concurrent_counters[key]
        
        info = {
            'limit': rule.limit,
            'current': current_count,
            'remaining': max(0, rule.limit - current_count)
        }
        
        if current_count >= rule.limit:
            return False, info
        
        return True, info
    
    def acquire_concurrent(self, key: str):
        """获取并发计数"""
        with self._lock:
            self.concurrent_counters[key] += 1
    
    def release_concurrent(self, key: str):
        """释放并发计数"""
        with self._lock:
            if self.concurrent_counters[key] > 0:
                self.concurrent_counters[key] -= 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取限流统计信息"""
        with self._lock:
            return {
                'rate_limit_keys': len(self.counters),
                'concurrent_keys': len(self.concurrent_counters),
                'total_concurrent': sum(self.concurrent_counters.values())
            }


class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        self.logger = get_logger('AuthManager')
        self.api_keys: Dict[str, Dict[str, Any]] = {}
        self.jwt_secret = "your-secret-key"  # 应该从配置中读取
        self.jwt_algorithm = "HS256"
        self.jwt_expiration = 3600  # 1小时
    
    def authenticate(self, request: APIRequest, auth_type: AuthType, auth_config: Dict[str, Any]) -> Tuple[bool, Optional[str], Dict[str, Any]]:
        """认证请求
        
        Args:
            request: API请求
            auth_type: 认证类型
            auth_config: 认证配置
        
        Returns:
            Tuple[bool, Optional[str], Dict[str, Any]]: (是否通过, 用户ID, 认证信息)
        """
        try:
            if auth_type == AuthType.NONE:
                return True, None, {}
            
            elif auth_type == AuthType.API_KEY:
                return self._authenticate_api_key(request, auth_config)
            
            elif auth_type == AuthType.JWT:
                return self._authenticate_jwt(request, auth_config)
            
            elif auth_type == AuthType.BASIC:
                return self._authenticate_basic(request, auth_config)
            
            elif auth_type == AuthType.CUSTOM:
                custom_handler = auth_config.get('handler')
                if custom_handler:
                    return custom_handler(request, auth_config)
            
            return False, None, {'error': f'不支持的认证类型: {auth_type.value}'}
        
        except Exception as e:
            self.logger.error(f"认证失败: {e}")
            return False, None, {'error': str(e)}
    
    def _authenticate_api_key(self, request: APIRequest, auth_config: Dict[str, Any]) -> Tuple[bool, Optional[str], Dict[str, Any]]:
        """API密钥认证"""
        api_key = request.api_key or request.headers.get('X-API-Key')
        
        if not api_key:
            return False, None, {'error': '缺少API密钥'}
        
        key_info = self.api_keys.get(api_key)
        if not key_info:
            return False, None, {'error': '无效的API密钥'}
        
        # 检查密钥是否过期
        if key_info.get('expires_at') and time.time() > key_info['expires_at']:
            return False, None, {'error': 'API密钥已过期'}
        
        # 检查密钥权限
        allowed_paths = key_info.get('allowed_paths', [])
        if allowed_paths and request.path not in allowed_paths:
            return False, None, {'error': '权限不足'}
        
        return True, key_info.get('user_id'), {'api_key': api_key, 'permissions': key_info.get('permissions', [])}
    
    def _authenticate_jwt(self, request: APIRequest, auth_config: Dict[str, Any]) -> Tuple[bool, Optional[str], Dict[str, Any]]:
        """JWT认证"""
        token = request.auth_token or request.headers.get('Authorization', '').replace('Bearer ', '')
        
        if not token:
            return False, None, {'error': '缺少JWT令牌'}
        
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            # 检查令牌是否过期
            if payload.get('exp') and time.time() > payload['exp']:
                return False, None, {'error': 'JWT令牌已过期'}
            
            return True, payload.get('user_id'), {'jwt_payload': payload}
        
        except jwt.InvalidTokenError as e:
            return False, None, {'error': f'无效的JWT令牌: {e}'}
    
    def _authenticate_basic(self, request: APIRequest, auth_config: Dict[str, Any]) -> Tuple[bool, Optional[str], Dict[str, Any]]:
        """基础认证"""
        auth_header = request.headers.get('Authorization', '')
        
        if not auth_header.startswith('Basic '):
            return False, None, {'error': '缺少基础认证信息'}
        
        try:
            import base64
            credentials = base64.b64decode(auth_header[6:]).decode('utf-8')
            username, password = credentials.split(':', 1)
            
            # 这里应该验证用户名和密码
            # 示例实现
            valid_users = auth_config.get('users', {})
            if username in valid_users and valid_users[username] == password:
                return True, username, {'username': username}
            
            return False, None, {'error': '用户名或密码错误'}
        
        except Exception as e:
            return False, None, {'error': f'基础认证失败: {e}'}
    
    def generate_jwt_token(self, user_id: str, permissions: List[str] = None) -> str:
        """生成JWT令牌"""
        payload = {
            'user_id': user_id,
            'permissions': permissions or [],
            'iat': time.time(),
            'exp': time.time() + self.jwt_expiration
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    def add_api_key(self, api_key: str, user_id: str, permissions: List[str] = None, expires_at: Optional[float] = None, allowed_paths: List[str] = None):
        """添加API密钥"""
        self.api_keys[api_key] = {
            'user_id': user_id,
            'permissions': permissions or [],
            'expires_at': expires_at,
            'allowed_paths': allowed_paths or [],
            'created_at': time.time()
        }
    
    def revoke_api_key(self, api_key: str):
        """撤销API密钥"""
        self.api_keys.pop(api_key, None)


class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self):
        self.logger = get_logger('APIGateway')
        self.request_stats = defaultdict(int)
        self._lock = threading.RLock()
    
    def log_request(self, request: APIRequest, response: APIResponse, processing_time: float):
        """记录请求日志"""
        with self._lock:
            # 更新统计信息
            self.request_stats['total_requests'] += 1
            self.request_stats[f'status_{response.status_code}'] += 1
            self.request_stats[f'method_{request.method.value}'] += 1
            
            # 记录详细日志
            log_data = {
                'request_id': request.request_id,
                'method': request.method.value,
                'path': request.path,
                'status_code': response.status_code,
                'processing_time': processing_time,
                'client_ip': request.client_ip,
                'user_agent': request.user_agent,
                'user_id': request.user_id,
                'cache_hit': response.cache_hit
            }
            
            if response.status_code >= 400:
                self.logger.error(f"API请求失败: {json.dumps(log_data, ensure_ascii=False)}")
            else:
                self.logger.info(f"API请求: {json.dumps(log_data, ensure_ascii=False)}")
            
            # 记录性能指标
            record_metric('api_request_duration', processing_time, {
                'method': request.method.value,
                'path': request.path,
                'status_code': str(response.status_code)
            })
            
            record_metric('api_request_count', 1, {
                'method': request.method.value,
                'path': request.path,
                'status_code': str(response.status_code)
            })
    
    def get_stats(self) -> Dict[str, Any]:
        """获取请求统计信息"""
        with self._lock:
            return dict(self.request_stats)


class APIGateway:
    """API网关"""
    
    def __init__(self):
        self.logger = get_logger('APIGateway')
        
        # 组件
        self.auth_manager = AuthManager()
        self.rate_limiter = RateLimiter()
        self.request_logger = RequestLogger()
        self.cache_manager = get_cache_manager()
        
        # 路由表
        self.endpoints: Dict[str, Dict[HTTPMethod, APIEndpoint]] = defaultdict(dict)
        
        # 全局中间件
        self.global_middlewares: List[Callable] = []
        
        # 配置
        self.config = {
            'enable_cors': True,
            'cors_origins': ['*'],
            'cors_methods': ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            'cors_headers': ['Content-Type', 'Authorization', 'X-API-Key'],
            'max_request_size': 10 * 1024 * 1024,  # 10MB
            'request_timeout': 30,  # 30秒
            'enable_compression': True,
            'compression_threshold': 1024  # 1KB
        }
        
        # 锁
        self._lock = threading.RLock()
    
    def register_endpoint(
        self,
        path: str,
        method: HTTPMethod,
        handler: Callable,
        auth_type: AuthType = AuthType.NONE,
        auth_config: Optional[Dict[str, Any]] = None,
        rate_limits: Optional[List[RateLimitRule]] = None,
        cache_enabled: bool = False,
        cache_ttl: int = 300,
        description: str = "",
        tags: Optional[List[str]] = None,
        middlewares: Optional[List[Callable]] = None
    ) -> APIEndpoint:
        """注册API端点
        
        Args:
            path: 路径
            method: HTTP方法
            handler: 处理函数
            auth_type: 认证类型
            auth_config: 认证配置
            rate_limits: 限流规则
            cache_enabled: 是否启用缓存
            cache_ttl: 缓存TTL
            description: 描述
            tags: 标签
            middlewares: 中间件
        
        Returns:
            APIEndpoint: API端点
        """
        endpoint = APIEndpoint(
            path=path,
            method=method,
            handler=handler,
            auth_type=auth_type,
            auth_config=auth_config or {},
            rate_limits=rate_limits or [],
            cache_enabled=cache_enabled,
            cache_ttl=cache_ttl,
            description=description,
            tags=tags or [],
            middlewares=middlewares or []
        )
        
        # 自动生成文档信息
        self._generate_endpoint_docs(endpoint)
        
        with self._lock:
            self.endpoints[path][method] = endpoint
        
        self.logger.info(f"注册API端点: {method.value} {path}")
        return endpoint
    
    def _generate_endpoint_docs(self, endpoint: APIEndpoint):
        """自动生成端点文档"""
        try:
            # 获取函数签名
            sig = inspect.signature(endpoint.handler)
            
            # 生成参数文档
            for param_name, param in sig.parameters.items():
                if param_name in ['request', 'self']:
                    continue
                
                param_doc = {
                    'name': param_name,
                    'type': str(param.annotation) if param.annotation != inspect.Parameter.empty else 'any',
                    'required': param.default == inspect.Parameter.empty,
                    'default': param.default if param.default != inspect.Parameter.empty else None
                }
                
                endpoint.parameters.append(param_doc)
            
            # 生成响应文档
            if not endpoint.responses:
                endpoint.responses = {
                    200: {'description': '成功', 'content': {'application/json': {}}},
                    400: {'description': '请求错误', 'content': {'application/json': {}}},
                    401: {'description': '未授权', 'content': {'application/json': {}}},
                    403: {'description': '禁止访问', 'content': {'application/json': {}}},
                    500: {'description': '服务器错误', 'content': {'application/json': {}}}
                }
        
        except Exception as e:
            self.logger.warning(f"生成端点文档失败: {e}")
    
    async def handle_request(self, request: APIRequest) -> APIResponse:
        """处理API请求
        
        Args:
            request: API请求
        
        Returns:
            APIResponse: API响应
        """
        start_time = time.time()
        
        try:
            # 查找端点
            endpoint = self._find_endpoint(request.path, request.method)
            if not endpoint:
                return APIResponse(
                    status_code=404,
                    body={'error': '端点不存在', 'path': request.path, 'method': request.method.value}
                )
            
            # 执行全局中间件
            for middleware in self.global_middlewares:
                result = await self._execute_middleware(middleware, request)
                if isinstance(result, APIResponse):
                    return result
            
            # 执行端点中间件
            for middleware in endpoint.middlewares:
                result = await self._execute_middleware(middleware, request)
                if isinstance(result, APIResponse):
                    return result
            
            # 认证
            auth_passed, user_id, auth_info = self.auth_manager.authenticate(
                request, endpoint.auth_type, endpoint.auth_config
            )
            
            if not auth_passed:
                return APIResponse(
                    status_code=401,
                    body={'error': '认证失败', 'details': auth_info}
                )
            
            request.user_id = user_id
            
            # 限流检查
            for rate_limit in endpoint.rate_limits:
                if not self._check_rate_limit(request, rate_limit):
                    return APIResponse(
                        status_code=429,
                        body={'error': '请求过于频繁', 'rate_limit': rate_limit.__dict__}
                    )
            
            # 缓存检查
            if endpoint.cache_enabled:
                cache_key = self._generate_cache_key(request, endpoint)
                cached_response = self.cache_manager.get(cache_key)
                
                if cached_response:
                    cached_response.cache_hit = True
                    return cached_response
            
            # 执行处理函数
            response = await self._execute_handler(endpoint.handler, request)
            
            # 缓存响应
            if endpoint.cache_enabled and response.status_code == 200:
                cache_key = self._generate_cache_key(request, endpoint)
                self.cache_manager.set(cache_key, response, ttl=endpoint.cache_ttl)
            
            return response
        
        except Exception as e:
            self.logger.error(f"处理API请求失败: {e}")
            return APIResponse(
                status_code=500,
                body={'error': '服务器内部错误', 'details': str(e)}
            )
        
        finally:
            # 记录请求日志
            processing_time = time.time() - start_time
            if 'response' in locals():
                response.processing_time = processing_time
                self.request_logger.log_request(request, response, processing_time)
    
    def _find_endpoint(self, path: str, method: HTTPMethod) -> Optional[APIEndpoint]:
        """查找端点"""
        with self._lock:
            # 精确匹配
            if path in self.endpoints and method in self.endpoints[path]:
                return self.endpoints[path][method]
            
            # 路径参数匹配（简单实现）
            for registered_path, methods in self.endpoints.items():
                if method in methods and self._path_matches(registered_path, path):
                    return methods[method]
            
            return None
    
    def _path_matches(self, pattern: str, path: str) -> bool:
        """路径匹配（支持路径参数）"""
        pattern_parts = pattern.split('/')
        path_parts = path.split('/')
        
        if len(pattern_parts) != len(path_parts):
            return False
        
        for pattern_part, path_part in zip(pattern_parts, path_parts):
            if pattern_part.startswith('{') and pattern_part.endswith('}'):
                # 路径参数，跳过
                continue
            elif pattern_part != path_part:
                return False
        
        return True
    
    def _check_rate_limit(self, request: APIRequest, rule: RateLimitRule) -> bool:
        """检查限流"""
        # 生成限流键
        keys = []
        
        if rule.apply_to_ip and request.client_ip:
            if request.client_ip not in rule.whitelist_ips:
                keys.append(f"ip:{request.client_ip}")
        
        if rule.apply_to_user and request.user_id:
            if request.user_id not in rule.whitelist_users:
                keys.append(f"user:{request.user_id}")
        
        if rule.apply_to_api_key and request.api_key:
            if request.api_key not in rule.whitelist_api_keys:
                keys.append(f"api_key:{request.api_key}")
        
        # 检查每个键的限流
        for key in keys:
            allowed, info = self.rate_limiter.is_allowed(key, rule)
            if not allowed:
                return False
        
        return True
    
    def _generate_cache_key(self, request: APIRequest, endpoint: APIEndpoint) -> str:
        """生成缓存键"""
        if endpoint.cache_key_generator:
            return endpoint.cache_key_generator(request)
        
        # 默认缓存键生成
        key_parts = [
            request.method.value,
            request.path,
            json.dumps(request.query_params, sort_keys=True),
            hashlib.md5(json.dumps(request.body, sort_keys=True).encode()).hexdigest() if request.body else ''
        ]
        
        return hashlib.md5('|'.join(key_parts).encode()).hexdigest()
    
    async def _execute_middleware(self, middleware: Callable, request: APIRequest) -> Optional[APIResponse]:
        """执行中间件"""
        try:
            if asyncio.iscoroutinefunction(middleware):
                result = await middleware(request)
            else:
                result = middleware(request)
            
            return result if isinstance(result, APIResponse) else None
        
        except Exception as e:
            self.logger.error(f"中间件执行失败: {e}")
            return APIResponse(
                status_code=500,
                body={'error': '中间件执行失败', 'details': str(e)}
            )
    
    async def _execute_handler(self, handler: Callable, request: APIRequest) -> APIResponse:
        """执行处理函数"""
        try:
            if asyncio.iscoroutinefunction(handler):
                result = await handler(request)
            else:
                result = handler(request)
            
            # 如果返回的不是APIResponse，则包装为APIResponse
            if not isinstance(result, APIResponse):
                return APIResponse(
                    status_code=200,
                    body=result
                )
            
            return result
        
        except Exception as e:
            self.logger.error(f"处理函数执行失败: {e}")
            return APIResponse(
                status_code=500,
                body={'error': '处理函数执行失败', 'details': str(e)}
            )
    
    def add_global_middleware(self, middleware: Callable):
        """添加全局中间件"""
        self.global_middlewares.append(middleware)
    
    def generate_openapi_spec(self) -> Dict[str, Any]:
        """生成OpenAPI规范"""
        spec = {
            'openapi': '3.0.0',
            'info': {
                'title': 'Lottery Predictor API',
                'version': '1.0.0',
                'description': '彩票预测系统API'
            },
            'paths': {},
            'components': {
                'securitySchemes': {
                    'ApiKeyAuth': {
                        'type': 'apiKey',
                        'in': 'header',
                        'name': 'X-API-Key'
                    },
                    'BearerAuth': {
                        'type': 'http',
                        'scheme': 'bearer',
                        'bearerFormat': 'JWT'
                    }
                }
            }
        }
        
        with self._lock:
            for path, methods in self.endpoints.items():
                spec['paths'][path] = {}
                
                for method, endpoint in methods.items():
                    operation = {
                        'summary': endpoint.description or f'{method.value} {path}',
                        'tags': endpoint.tags,
                        'parameters': endpoint.parameters,
                        'responses': endpoint.responses
                    }
                    
                    # 添加安全要求
                    if endpoint.auth_type == AuthType.API_KEY:
                        operation['security'] = [{'ApiKeyAuth': []}]
                    elif endpoint.auth_type == AuthType.JWT:
                        operation['security'] = [{'BearerAuth': []}]
                    
                    spec['paths'][path][method.value.lower()] = operation
        
        return spec
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                'endpoints': len(sum(len(methods) for methods in self.endpoints.values())),
                'paths': len(self.endpoints),
                'request_stats': self.request_logger.get_stats(),
                'rate_limit_stats': self.rate_limiter.get_stats(),
                'cache_stats': self.cache_manager.get_stats() if hasattr(self.cache_manager, 'get_stats') else {},
                'global_middlewares': len(self.global_middlewares)
            }
    
    def export_config(self, file_path: str):
        """导出配置"""
        try:
            config_data = {
                'gateway_config': self.config,
                'endpoints': [],
                'statistics': self.get_statistics()
            }
            
            with self._lock:
                for path, methods in self.endpoints.items():
                    for method, endpoint in methods.items():
                        endpoint_data = {
                            'path': path,
                            'method': method.value,
                            'auth_type': endpoint.auth_type.value,
                            'rate_limits': [rule.__dict__ for rule in endpoint.rate_limits],
                            'cache_enabled': endpoint.cache_enabled,
                            'cache_ttl': endpoint.cache_ttl,
                            'description': endpoint.description,
                            'tags': endpoint.tags
                        }
                        
                        config_data['endpoints'].append(endpoint_data)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"API网关配置已导出到: {file_path}")
        
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            raise SystemException(f"导出配置失败: {e}", ErrorCode.FILE_WRITE_ERROR)


# 装饰器
def api_endpoint(
    path: str,
    method: HTTPMethod = HTTPMethod.GET,
    auth_type: AuthType = AuthType.NONE,
    auth_config: Optional[Dict[str, Any]] = None,
    rate_limits: Optional[List[RateLimitRule]] = None,
    cache_enabled: bool = False,
    cache_ttl: int = 300,
    description: str = "",
    tags: Optional[List[str]] = None
):
    """API端点装饰器
    
    Args:
        path: 路径
        method: HTTP方法
        auth_type: 认证类型
        auth_config: 认证配置
        rate_limits: 限流规则
        cache_enabled: 是否启用缓存
        cache_ttl: 缓存TTL
        description: 描述
        tags: 标签
    
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        # 自动注册端点
        get_api_gateway().register_endpoint(
            path=path,
            method=method,
            handler=func,
            auth_type=auth_type,
            auth_config=auth_config,
            rate_limits=rate_limits,
            cache_enabled=cache_enabled,
            cache_ttl=cache_ttl,
            description=description,
            tags=tags
        )
        
        return func
    return decorator


def require_auth(auth_type: AuthType, **auth_config):
    """认证装饰器
    
    Args:
        auth_type: 认证类型
        **auth_config: 认证配置
    
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(request: APIRequest, *args, **kwargs):
            gateway = get_api_gateway()
            auth_passed, user_id, auth_info = gateway.auth_manager.authenticate(
                request, auth_type, auth_config
            )
            
            if not auth_passed:
                return APIResponse(
                    status_code=401,
                    body={'error': '认证失败', 'details': auth_info}
                )
            
            request.user_id = user_id
            
            if asyncio.iscoroutinefunction(func):
                return await func(request, *args, **kwargs)
            else:
                return func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def rate_limit(limit_type: RateLimitType, limit: int, window_size: int = 60):
    """限流装饰器
    
    Args:
        limit_type: 限流类型
        limit: 限制数量
        window_size: 时间窗口
    
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(request: APIRequest, *args, **kwargs):
            rule = RateLimitRule(type=limit_type, limit=limit, window_size=window_size)
            gateway = get_api_gateway()
            
            if not gateway._check_rate_limit(request, rule):
                return APIResponse(
                    status_code=429,
                    body={'error': '请求过于频繁'}
                )
            
            if asyncio.iscoroutinefunction(func):
                return await func(request, *args, **kwargs)
            else:
                return func(request, *args, **kwargs)
        
        return wrapper
    return decorator


# 全局API网关实例
_api_gateway = None


def get_api_gateway() -> APIGateway:
    """获取API网关实例
    
    Returns:
        APIGateway: API网关实例
    """
    global _api_gateway
    if _api_gateway is None:
        _api_gateway = APIGateway()
    return _api_gateway


# 导出的公共接口
__all__ = [
    'HTTPMethod',
    'AuthType',
    'RateLimitType',
    'ResponseFormat',
    'APIRequest',
    'APIResponse',
    'RateLimitRule',
    'APIEndpoint',
    'RateLimiter',
    'AuthManager',
    'RequestLogger',
    'APIGateway',
    'api_endpoint',
    'require_auth',
    'rate_limit',
    'get_api_gateway'
]