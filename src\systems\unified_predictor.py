"""
增强版统一预测器
整合比值预测、增强算法、集成学习、自适应调优和性能监控，提供完整的预测解决方案
"""

import pandas as pd
from typing import Dict, List, Any, Optional
import asyncio
from pathlib import Path

from ..predictors.ratio_predictor import RatioPredictor
from ..async_framework.async_predictor import AsyncPredictorAdapter, AsyncBacktestFramework
from ..logging.simple_logger import SimplePredictionLogger, BacktestLogger
from ..config.config_manager import ConfigManager
from ..core.interfaces import IPredictor, PredictionResult
from ..algorithms.enhanced_markov_chain import EnhancedMarkovChain
from ..algorithms.enhanced_bayesian import EnhancedBayesianPredictor
from ..algorithms.enhanced_ensemble import EnhancedEnsembleLearning
from ..algorithms.adaptive_tuner import AdaptiveParameterTuner
from ..algorithms.performance_monitor import PerformanceMonitor
from ..utils.kill_algorithms import UnifiedKillAlgorithms


class UnifiedPredictor:
    """增强版统一预测器"""
    
    def __init__(self, config_dir: str = "config"):
        # 初始化配置管理器
        self.config_manager = ConfigManager(config_dir)
        
        # 初始化基础预测器
        self.ratio_predictor = RatioPredictor(self.config_manager.get_config('algorithms', {}))
        
        # 初始化增强算法组件
        self.markov_chain = EnhancedMarkovChain(
            order=self.config_manager.get_config('markov.order', 2),
            state_types=['ratio', 'pattern', 'sequence']
        )
        
        self.bayesian_predictor = EnhancedBayesianPredictor(
            network_layers=self.config_manager.get_config('bayesian.layers', 3),
            evidence_weight=self.config_manager.get_config('bayesian.evidence_weight', 0.7)
        )
        
        # 初始化集成学习管理器
        self.ensemble_manager = EnhancedEnsembleManager(
            min_algorithms=self.config_manager.get_config('ensemble.min_algorithms', 3),
            performance_threshold=self.config_manager.get_config('ensemble.performance_threshold', 0.6)
        )
        
        # 初始化自适应参数调优器
        self.adaptive_tuner = AdaptiveParameterTuner(
            optimization_method=self.config_manager.get_config('tuner.method', 'bayesian'),
            max_iterations=self.config_manager.get_config('tuner.max_iterations', 100)
        )
        
        # 初始化性能监控器
        self.performance_monitor = PerformanceMonitor(
            alert_threshold=self.config_manager.get_config('monitor.alert_threshold', 0.5),
            baseline_window=self.config_manager.get_config('monitor.baseline_window', 50)
        )
        
        # 初始化杀号算法
        self.kill_algorithms = UnifiedKillAlgorithms()
        
        # 初始化异步适配器
        max_workers = self.config_manager.get_config('prediction.max_workers', 4)
        self.async_predictor = AsyncPredictorAdapter(
            self.ratio_predictor, 
            max_workers=max_workers
        )
        
        # 初始化日志系统
        log_config = self.config_manager.get_config('output', {})
        self.logger = SimplePredictionLogger(
            enable_console=True,
            log_file=log_config.get('log_file')
        )
        
        # 初始化回测框架
        self.backtest_framework = AsyncBacktestFramework(max_workers)
        
        # 数据缓存
        self._data_cache: Optional[pd.DataFrame] = None
        
        # 注册算法到集成管理器
        self._register_algorithms()
    
    def _register_algorithms(self):
        """注册算法到集成管理器"""
        # 注册基础比值预测器
        self.ensemble_manager.register_algorithm(
            'ratio_predictor', 
            self.ratio_predictor,
            weight=0.3
        )
        
        # 注册马尔科夫链
        self.ensemble_manager.register_algorithm(
            'markov_chain',
            self.markov_chain,
            weight=0.25
        )
        
        # 注册贝叶斯预测器
        self.ensemble_manager.register_algorithm(
            'bayesian_predictor',
            self.bayesian_predictor,
            weight=0.25
        )
        
        # 注册杀号算法
        self.ensemble_manager.register_algorithm(
            'kill_algorithms',
            self.kill_algorithms,
            weight=0.2
        )
    
    def load_data(self, data_path: str) -> bool:
        """加载数据"""
        try:
            data_file = Path(data_path)
            if not data_file.exists():
                self.logger.log_error(Exception(f"数据文件不存在: {data_path}"))
                return False
            
            # 读取数据
            self._data_cache = pd.read_csv(data_path, encoding='utf-8')
            
            # 验证数据格式
            required_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
            missing_columns = [col for col in required_columns if col not in self._data_cache.columns]
            
            if missing_columns:
                self.logger.log_error(Exception(f"数据缺少必需列: {missing_columns}"))
                return False
            
            # 确保数据按期号排序（最新在前）
            self._data_cache = self._data_cache.sort_values('期号', ascending=False).reset_index(drop=True)
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, {'data_path': data_path})
            return False
    
    def predict_next_period(self, base_period: Optional[str] = None, use_ensemble: bool = True) -> Dict[str, Any]:
        """增强版预测下一期"""
        if self._data_cache is None or len(self._data_cache) == 0:
            raise ValueError("请先加载数据")
        
        # 确定基准期
        if base_period is None:
            # 使用最新期作为基准
            target_index = 0
            base_period = str(self._data_cache.iloc[0]['期号'])
        else:
            # 查找指定期号的索引
            period_mask = self._data_cache['期号'] == int(base_period)
            if not period_mask.any():
                raise ValueError(f"找不到期号: {base_period}")
            target_index = self._data_cache[period_mask].index[0]
        
        # 计算下一期期号
        next_period = str(int(base_period) + 1)
        
        if use_ensemble:
            # 使用集成学习预测
            ensemble_predictions = self.ensemble_manager.predict_ensemble(
                self._data_cache, target_index
            )
            
            # 生成杀号建议
            red_kills = self.kill_algorithms.generate_red_kill_numbers(
                self._data_cache, target_index
            )
            blue_kills = self.kill_algorithms.generate_blue_kill_numbers(
                self._data_cache, target_index
            )
            
            # 更新性能监控
            self.performance_monitor.record_prediction(
                base_period, next_period, ensemble_predictions
            )
            
            # 记录预测结果
            self.logger.log_prediction_results(base_period, next_period, ensemble_predictions)
            
            return {
                'base_period': base_period,
                'next_period': next_period,
                'ensemble_predictions': ensemble_predictions,
                'red_kill_numbers': red_kills,
                'blue_kill_numbers': blue_kills,
                'confidence_scores': self.ensemble_manager.get_confidence_scores(),
                'algorithm_weights': self.ensemble_manager.get_current_weights()
            }
        else:
            # 使用基础比值预测
            predictions = self.ratio_predictor.predict_all_ratios(self._data_cache, target_index)
            
            # 记录预测结果
            self.logger.log_prediction_results(base_period, next_period, predictions)
            self.logger.log_ratio_state_mapping()
            
            return {
                'base_period': base_period,
                'next_period': next_period,
                'predictions': predictions
            }
    
    async def run_backtest(self, periods: int = 10) -> Dict[str, Any]:
        """运行回测"""
        if self._data_cache is None or len(self._data_cache) == 0:
            raise ValueError("请先加载数据")
        
        if periods > len(self._data_cache) - 1:
            periods = len(self._data_cache) - 1
        
        # 创建回测日志记录器
        backtest_logger = BacktestLogger(self.logger)
        
        # 执行回测
        backtest_results = []
        
        for i in range(periods):
            # 计算目标索引：从数据末尾开始往前回测
            target_index = len(self._data_cache) - periods + i
            
            if target_index <= 0:
                continue
            
            # 获取基准期和目标期
            base_period = str(self._data_cache.iloc[target_index + 1]['期号'])
            target_period = str(self._data_cache.iloc[target_index]['期号'])
            actual_data = self._data_cache.iloc[target_index]
            
            # 执行预测
            predictions = self.ratio_predictor.predict_all_ratios(self._data_cache, target_index)
            
            # 记录结果
            backtest_logger.log_period_result(base_period, target_period, predictions, actual_data)
            
            backtest_results.append({
                'base_period': base_period,
                'target_period': target_period,
                'predictions': predictions,
                'actual_data': actual_data
            })
        
        # 记录回测摘要
        backtest_logger.log_summary()
        
        return {
            'total_periods': len(backtest_results),
            'hit_rates': backtest_logger.get_hit_rates(),
            'results': backtest_results
        }
    
    async def run_async_backtest(self, periods: int = 10) -> Dict[str, Any]:
        """运行异步回测"""
        if self._data_cache is None or len(self._data_cache) == 0:
            raise ValueError("请先加载数据")
        
        # 创建异步预测器适配器
        async_predictor = AsyncPredictorAdapter(self.ratio_predictor)
        
        # 运行异步回测
        results = await self.backtest_framework.run_backtest_async(
            async_predictor, 
            self._data_cache, 
            periods
        )
        
        return results
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息"""
        if self._data_cache is None:
            return {'loaded': False}
        
        return {
            'loaded': True,
            'total_records': len(self._data_cache),
            'latest_period': str(self._data_cache.iloc[0]['期号']),
            'earliest_period': str(self._data_cache.iloc[-1]['期号']),
            'columns': list(self._data_cache.columns)
        }
    
    def validate_data_consistency(self) -> List[str]:
        """验证数据一致性"""
        if self._data_cache is None:
            return ["数据未加载"]
        
        issues = []
        
        # 检查期号连续性
        periods = self._data_cache['期号'].tolist()
        for i in range(len(periods) - 1):
            current = periods[i]
            next_period = periods[i + 1]
            
            # 期号应该是递减的（最新在前）
            if current <= next_period:
                issues.append(f"期号顺序异常: {current} -> {next_period}")
        
        # 检查数据范围
        for _, row in self._data_cache.iterrows():
            period = row['期号']
            
            # 检查红球范围
            for i in range(1, 6):
                red_ball = row[f'红球{i}']
                if not (1 <= red_ball <= 35):
                    issues.append(f"期号{period}红球{i}超出范围: {red_ball}")
            
            # 检查蓝球范围
            for i in range(1, 3):
                blue_ball = row[f'蓝球{i}']
                if not (1 <= blue_ball <= 12):
                    issues.append(f"期号{period}蓝球{i}超出范围: {blue_ball}")
        
        return issues
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置"""
        return self.config_manager.get_config(key, default)
    
    def set_config(self, key: str, value: Any) -> None:
        """设置配置"""
        self.config_manager.set_config(key, value)
    
    def optimize_parameters(self, target_metric: str = 'accuracy') -> Dict[str, Any]:
        """自适应参数优化"""
        if self._data_cache is None or len(self._data_cache) == 0:
            raise ValueError("请先加载数据")
        
        # 定义优化目标函数
        def objective_function(params: Dict[str, Any]) -> float:
            # 临时更新参数
            old_params = {}
            for key, value in params.items():
                old_params[key] = self.get_config(key)
                self.set_config(key, value)
            
            try:
                # 运行小规模回测评估性能
                results = asyncio.run(self.run_backtest(periods=10))
                metric_value = results['hit_rates'].get(target_metric, 0.0)
                return metric_value
            finally:
                # 恢复原参数
                for key, value in old_params.items():
                    self.set_config(key, value)
        
        # 执行参数优化
        optimization_results = self.adaptive_tuner.optimize_parameters(
            objective_function,
            target_metric
        )
        
        return optimization_results
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        return self.performance_monitor.generate_performance_report()
    
    def update_kill_strategy_performance(self, period: str, actual_results: Dict[str, Any]) -> None:
        """更新杀号策略性能"""
        self.kill_algorithms.update_strategy_performance(period, actual_results)
    
    def get_algorithm_insights(self) -> Dict[str, Any]:
        """获取算法洞察"""
        return {
            'ensemble_info': self.ensemble_manager.get_ensemble_info(),
            'markov_info': self.markov_chain.get_model_info(),
            'bayesian_info': self.bayesian_predictor.get_model_info(),
            'kill_algorithms_info': {
                'strategy_weights': self.kill_algorithms.strategy_weights,
                'strategy_performance': self.kill_algorithms.strategy_performance
            },
            'performance_metrics': self.performance_monitor.get_current_metrics()
        }
    
    def train_enhanced_models(self, training_periods: int = 100) -> Dict[str, Any]:
        """训练增强模型"""
        if self._data_cache is None or len(self._data_cache) == 0:
            raise ValueError("请先加载数据")
        
        training_data = self._data_cache.tail(training_periods)
        
        # 训练马尔科夫链
        markov_results = self.markov_chain.train(training_data)
        
        # 训练贝叶斯预测器
        bayesian_results = self.bayesian_predictor.train_model(training_data)
        
        # 更新集成权重
        ensemble_results = self.ensemble_manager.update_weights_based_on_performance()
        
        return {
            'markov_training': markov_results,
            'bayesian_training': bayesian_results,
            'ensemble_update': ensemble_results,
            'training_periods': training_periods
        }
    
    def export_model_state(self, export_path: str) -> bool:
        """导出模型状态"""
        try:
            model_state = {
                'config': self.config_manager.get_all_configs(),
                'ensemble_weights': self.ensemble_manager.get_current_weights(),
                'kill_strategy_weights': self.kill_algorithms.strategy_weights,
                'performance_metrics': self.performance_monitor.get_current_metrics()
            }
            
            import json
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(model_state, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            self.logger.log_error(e, {'export_path': export_path})
            return False
    
    def load_model_state(self, import_path: str) -> bool:
        """加载模型状态"""
        try:
            import json
            with open(import_path, 'r', encoding='utf-8') as f:
                model_state = json.load(f)
            
            # 恢复配置
            if 'config' in model_state:
                for key, value in model_state['config'].items():
                    self.set_config(key, value)
            
            # 恢复集成权重
            if 'ensemble_weights' in model_state:
                self.ensemble_manager.set_weights(model_state['ensemble_weights'])
            
            # 恢复杀号策略权重
            if 'kill_strategy_weights' in model_state:
                self.kill_algorithms.strategy_weights = model_state['kill_strategy_weights']
            
            return True
        except Exception as e:
            self.logger.log_error(e, {'import_path': import_path})
            return False