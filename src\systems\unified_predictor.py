"""
统一预测器
整合比值预测、异步处理和简洁日志，提供完整的预测解决方案
"""

import pandas as pd
from typing import Dict, List, Any, Optional
import asyncio
from pathlib import Path

from ..predictors.ratio_predictor import RatioPredictor
from ..async_framework.async_predictor import AsyncPredictorAdapter, AsyncBacktestFramework
from ..logging.simple_logger import SimplePredictionLogger, BacktestLogger
from ..config.config_manager import ConfigManager
from ..core.interfaces import IPredictor, PredictionResult


class UnifiedPredictor:
    """统一预测器"""
    
    def __init__(self, config_dir: str = "config"):
        # 初始化配置管理器
        self.config_manager = ConfigManager(config_dir)
        
        # 初始化预测器
        self.ratio_predictor = RatioPredictor(self.config_manager.get_config('algorithms', {}))
        
        # 初始化异步适配器
        max_workers = self.config_manager.get_config('prediction.max_workers', 4)
        self.async_predictor = AsyncPredictorAdapter(
            self.ratio_predictor, 
            max_workers=max_workers
        )
        
        # 初始化日志系统
        log_config = self.config_manager.get_config('output', {})
        self.logger = SimplePredictionLogger(
            enable_console=True,
            log_file=log_config.get('log_file')
        )
        
        # 初始化回测框架
        self.backtest_framework = AsyncBacktestFramework(max_workers)
        
        # 数据缓存
        self._data_cache: Optional[pd.DataFrame] = None
    
    def load_data(self, data_path: str) -> bool:
        """加载数据"""
        try:
            data_file = Path(data_path)
            if not data_file.exists():
                self.logger.log_error(Exception(f"数据文件不存在: {data_path}"))
                return False
            
            # 读取数据
            self._data_cache = pd.read_csv(data_path, encoding='utf-8')
            
            # 验证数据格式
            required_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
            missing_columns = [col for col in required_columns if col not in self._data_cache.columns]
            
            if missing_columns:
                self.logger.log_error(Exception(f"数据缺少必需列: {missing_columns}"))
                return False
            
            # 确保数据按期号排序（最新在前）
            self._data_cache = self._data_cache.sort_values('期号', ascending=False).reset_index(drop=True)
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, {'data_path': data_path})
            return False
    
    def predict_next_period(self, base_period: Optional[str] = None) -> Dict[str, PredictionResult]:
        """预测下一期"""
        if self._data_cache is None or len(self._data_cache) == 0:
            raise ValueError("请先加载数据")
        
        # 确定基准期
        if base_period is None:
            # 使用最新期作为基准
            target_index = 0
            base_period = str(self._data_cache.iloc[0]['期号'])
        else:
            # 查找指定期号的索引
            period_mask = self._data_cache['期号'] == int(base_period)
            if not period_mask.any():
                raise ValueError(f"找不到期号: {base_period}")
            target_index = self._data_cache[period_mask].index[0]
        
        # 执行预测
        predictions = self.ratio_predictor.predict_all_ratios(self._data_cache, target_index)
        
        # 计算下一期期号
        next_period = str(int(base_period) + 1)
        
        # 记录预测结果
        self.logger.log_prediction_results(base_period, next_period, predictions)
        self.logger.log_ratio_state_mapping()
        
        return predictions
    
    async def run_backtest(self, periods: int = 10) -> Dict[str, Any]:
        """运行回测"""
        if self._data_cache is None or len(self._data_cache) == 0:
            raise ValueError("请先加载数据")
        
        if periods > len(self._data_cache) - 1:
            periods = len(self._data_cache) - 1
        
        # 创建回测日志记录器
        backtest_logger = BacktestLogger(self.logger)
        
        # 执行回测
        backtest_results = []
        
        for i in range(periods):
            # 计算目标索引：从数据末尾开始往前回测
            target_index = len(self._data_cache) - periods + i
            
            if target_index <= 0:
                continue
            
            # 获取基准期和目标期
            base_period = str(self._data_cache.iloc[target_index + 1]['期号'])
            target_period = str(self._data_cache.iloc[target_index]['期号'])
            actual_data = self._data_cache.iloc[target_index]
            
            # 执行预测
            predictions = self.ratio_predictor.predict_all_ratios(self._data_cache, target_index)
            
            # 记录结果
            backtest_logger.log_period_result(base_period, target_period, predictions, actual_data)
            
            backtest_results.append({
                'base_period': base_period,
                'target_period': target_period,
                'predictions': predictions,
                'actual_data': actual_data
            })
        
        # 记录回测摘要
        backtest_logger.log_summary()
        
        return {
            'total_periods': len(backtest_results),
            'hit_rates': backtest_logger.get_hit_rates(),
            'results': backtest_results
        }
    
    async def run_async_backtest(self, periods: int = 10) -> Dict[str, Any]:
        """运行异步回测"""
        if self._data_cache is None or len(self._data_cache) == 0:
            raise ValueError("请先加载数据")
        
        # 创建异步预测器适配器
        async_predictor = AsyncPredictorAdapter(self.ratio_predictor)
        
        # 运行异步回测
        results = await self.backtest_framework.run_backtest_async(
            async_predictor, 
            self._data_cache, 
            periods
        )
        
        return results
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息"""
        if self._data_cache is None:
            return {'loaded': False}
        
        return {
            'loaded': True,
            'total_records': len(self._data_cache),
            'latest_period': str(self._data_cache.iloc[0]['期号']),
            'earliest_period': str(self._data_cache.iloc[-1]['期号']),
            'columns': list(self._data_cache.columns)
        }
    
    def validate_data_consistency(self) -> List[str]:
        """验证数据一致性"""
        if self._data_cache is None:
            return ["数据未加载"]
        
        issues = []
        
        # 检查期号连续性
        periods = self._data_cache['期号'].tolist()
        for i in range(len(periods) - 1):
            current = periods[i]
            next_period = periods[i + 1]
            
            # 期号应该是递减的（最新在前）
            if current <= next_period:
                issues.append(f"期号顺序异常: {current} -> {next_period}")
        
        # 检查数据范围
        for _, row in self._data_cache.iterrows():
            period = row['期号']
            
            # 检查红球范围
            for i in range(1, 6):
                red_ball = row[f'红球{i}']
                if not (1 <= red_ball <= 35):
                    issues.append(f"期号{period}红球{i}超出范围: {red_ball}")
            
            # 检查蓝球范围
            for i in range(1, 3):
                blue_ball = row[f'蓝球{i}']
                if not (1 <= blue_ball <= 12):
                    issues.append(f"期号{period}蓝球{i}超出范围: {blue_ball}")
        
        return issues
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置"""
        return self.config_manager.get_config(key, default)
    
    def set_config(self, key: str, value: Any) -> None:
        """设置配置"""
        self.config_manager.set_config(key, value)