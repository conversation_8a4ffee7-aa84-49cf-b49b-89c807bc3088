"""简化的集成测试
测试重构后架构的基本功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.core import (
        DependencyContainer, ConfigurationManager, ConfigurablePredictorFactory,
        BallType, PredictionType, StandardBasePredictor
    )
    from src.core.interfaces import IStandardPredictor, ValidationResult
    from src.core.predictor_adapter import create_legacy_adapter
    from src.core.exceptions import PredictionException, ValidationException
    print("* 导入成功")
except Exception as e:
    print(f"* 导入失败: {e}")
    sys.exit(1)


def create_sample_data():
    """创建测试数据"""
    np.random.seed(42)
    data = []
    for i in range(50):
        period = f"25{i:03d}"
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        data.append({
            'period': period,
            'red_1': red_balls[0], 'red_2': red_balls[1], 'red_3': red_balls[2],
            'red_4': red_balls[3], 'red_5': red_balls[4],
            'blue_1': blue_balls[0], 'blue_2': blue_balls[1]
        })
    return pd.DataFrame(data)


def test_basic_components():
    """测试基本组件"""
    print("测试基本组件...")
    
    try:
        # 测试依赖注入容器
        container = DependencyContainer()
        print("  - 依赖注入容器: OK")
        
        # 测试配置管理器
        config_manager = ConfigurationManager()
        config = config_manager.load_config()
        print("  - 配置管理器: OK")
        
        # 测试预测器工厂
        factory = ConfigurablePredictorFactory()
        available = factory.get_available_predictors()
        print(f"  - 预测器工厂: OK (可用预测器: {len(available)})")
        
        return True
    except Exception as e:
        print(f"  - 基本组件测试失败: {e}")
        return False


def test_legacy_adapter():
    """测试旧预测器适配"""
    print("测试旧预测器适配...")
    
    try:
        # 创建模拟旧预测器
        class MockLegacyPredictor:
            def __init__(self):
                self.config = {"test": True}
                
            def predict(self, data, target_index, **kwargs):
                return {"value": "1:4", "confidence": 0.8}
        
        # 创建适配器
        legacy_predictor = MockLegacyPredictor()
        adapter = create_legacy_adapter(legacy_predictor, "TestAdapter")
        
        # 测试适配器
        sample_data = create_sample_data()
        validation = adapter.validate_input(sample_data, 10)
        assert validation.is_valid
        
        result = adapter.predict(sample_data, 10, ball_type=BallType.RED)
        assert result.value == "1:4"
        
        print("  - 旧预测器适配: OK")
        return True
    except Exception as e:
        print(f"  - 旧预测器适配失败: {e}")
        return False


def test_standard_predictor():
    """测试标准预测器"""
    print("测试标准预测器...")
    
    try:
        class TestPredictor(StandardBasePredictor):
            def __init__(self):
                super().__init__("TestPredictor", PredictionType.NUMBERS)
            
            def predict(self, data, target_index, **kwargs):
                from src.core.interfaces import PredictionResult as StandardPredictionResult
                return StandardPredictionResult(
                    prediction_type=self.prediction_type,
                    ball_type=kwargs.get('ball_type', BallType.RED),
                    value=[1, 2, 3, 4, 5],
                    confidence=0.7,
                    metadata={"test": True}
                )
        
        predictor = TestPredictor()
        sample_data = create_sample_data()
        
        # 测试预测
        result = predictor.predict(sample_data, 10, ball_type=BallType.RED)
        assert result.value == [1, 2, 3, 4, 5]
        
        # 测试批量预测
        batch_results = predictor.predict_batch(sample_data, [10, 11])
        assert len(batch_results) == 2
        
        print("  - 标准预测器: OK")
        return True
    except Exception as e:
        print(f"  - 标准预测器失败: {e}")
        return False


def test_refactored_system():
    """测试重构后系统"""
    print("测试重构后系统...")
    
    try:
        from src.systems.refactored_main import RefactoredLotterySystem
        system = RefactoredLotterySystem()
        assert system.config is not None
        print("  - 重构后系统: OK")
        return True
    except Exception as e:
        print(f"  - 重构后系统失败: {e}")
        return False


def main():
    """主函数"""
    print("* 开始简化集成测试")
    print("=" * 40)
    
    tests = [
        ("基本组件", test_basic_components),
        ("旧预测器适配", test_legacy_adapter),
        ("标准预测器", test_standard_predictor),
        ("重构后系统", test_refactored_system),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("* 所有测试通过！架构重构成功！")
    else:
        print(f"* {total - passed} 个测试失败")


if __name__ == "__main__":
    main()