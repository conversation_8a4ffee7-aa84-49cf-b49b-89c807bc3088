#!/usr/bin/env python3
"""
LSTM回测系统
LSTM Backtest System

使用LSTM深度学习模型进行彩票预测的回测验证
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/lstm_backtest.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class LSTMBacktestSystem:
    """LSTM回测系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"lstm_backtest.{self.__class__.__name__}")
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        data_path = Path("data/raw/dlt_data.csv")
        if data_path.exists():
            data = pd.read_csv(data_path)
            self.logger.info(f"加载数据: {len(data)} 条记录")
            return data
        else:
            self.logger.error("数据文件不存在")
            return pd.DataFrame()
    
    def parse_numbers(self, row: pd.Series) -> Tuple[List[int], List[int]]:
        """解析红球和蓝球数字"""
        red_numbers = []
        for i in range(1, 6):  # 红球1到红球5
            col_name = f'红球{i}'
            if col_name in row and pd.notna(row[col_name]):
                red_numbers.append(int(row[col_name]))
        
        blue_numbers = []
        for i in range(1, 3):  # 蓝球1到蓝球2
            col_name = f'蓝球{i}'
            if col_name in row and pd.notna(row[col_name]):
                blue_numbers.append(int(row[col_name]))
        
        return red_numbers, blue_numbers
    
    def calculate_ratios(self, red_numbers: List[int], blue_numbers: List[int]) -> Dict[str, str]:
        """计算各种比例"""
        ratios = {}
        
        if len(red_numbers) >= 5:
            # 红球奇偶比
            odd_count = sum(1 for num in red_numbers if num % 2 == 1)
            even_count = 5 - odd_count
            ratios['red_odd_even'] = f"{odd_count}:{even_count}"
            
            # 红球大小比
            big_count = sum(1 for num in red_numbers if num > 17)
            small_count = 5 - big_count
            ratios['red_size'] = f"{small_count}:{big_count}"
        
        if len(blue_numbers) >= 2:
            # 蓝球大小比
            big_count = sum(1 for num in blue_numbers if num > 6)
            small_count = 2 - big_count
            ratios['blue_size'] = f"{small_count}:{big_count}"
        
        return ratios
    
    def prepare_lstm_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """准备LSTM特征数据"""
        features = []
        
        for _, row in data.iterrows():
            red_numbers, blue_numbers = self.parse_numbers(row)
            
            if len(red_numbers) >= 5 and len(blue_numbers) >= 2:
                # 基础特征
                feature_row = {
                    '期号': row['期号'],
                    # 红球特征
                    'red_sum': sum(red_numbers),
                    'red_mean': np.mean(red_numbers),
                    'red_std': np.std(red_numbers),
                    'red_min': min(red_numbers),
                    'red_max': max(red_numbers),
                    'red_range': max(red_numbers) - min(red_numbers),
                    # 红球奇偶特征
                    'red_odd_count': sum(1 for num in red_numbers if num % 2 == 1),
                    'red_even_count': sum(1 for num in red_numbers if num % 2 == 0),
                    # 红球大小特征
                    'red_big_count': sum(1 for num in red_numbers if num > 17),
                    'red_small_count': sum(1 for num in red_numbers if num <= 17),
                    # 蓝球特征
                    'blue_sum': sum(blue_numbers),
                    'blue_mean': np.mean(blue_numbers),
                    'blue_min': min(blue_numbers),
                    'blue_max': max(blue_numbers),
                    # 蓝球大小特征
                    'blue_big_count': sum(1 for num in blue_numbers if num > 6),
                    'blue_small_count': sum(1 for num in blue_numbers if num <= 6),
                    # 连号特征
                    'red_consecutive': self._count_consecutive(red_numbers),
                    'blue_consecutive': self._count_consecutive(blue_numbers),
                    # 跨度特征
                    'red_span': max(red_numbers) - min(red_numbers),
                    'blue_span': max(blue_numbers) - min(blue_numbers) if len(blue_numbers) > 1 else 0
                }
                
                # 添加比例特征
                ratios = self.calculate_ratios(red_numbers, blue_numbers)
                feature_row.update({
                    'red_odd_even_ratio': ratios.get('red_odd_even', '0:0'),
                    'red_size_ratio': ratios.get('red_size', '0:0'),
                    'blue_size_ratio': ratios.get('blue_size', '0:0')
                })
                
                features.append(feature_row)
        
        return pd.DataFrame(features)
    
    def _count_consecutive(self, numbers: List[int]) -> int:
        """计算连续数字的数量"""
        if len(numbers) < 2:
            return 0
        
        sorted_nums = sorted(numbers)
        consecutive_count = 0
        
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        
        return consecutive_count
    
    def create_lstm_predictor(self) -> Any:
        """创建LSTM预测器"""
        try:
            from src.models.deep_learning.lstm_predictor import LSTMPredictor, LSTMConfig
            
            # 配置LSTM参数
            config = LSTMConfig(
                lstm_units=64,
                lstm_layers=2,
                dense_units=32,
                dropout_rate=0.2,
                sequence_length=10,  # 使用最近10期数据
                learning_rate=0.001,
                epochs=50,
                batch_size=16,
                validation_split=0.2
            )
            
            return LSTMPredictor(config)
            
        except ImportError as e:
            self.logger.error(f"无法导入LSTM预测器: {e}")
            return None
    
    def lstm_ratio_prediction(self, features_df: pd.DataFrame, target_index: int, ratio_type: str) -> str:
        """使用LSTM预测比例"""
        try:
            predictor = self.create_lstm_predictor()
            if predictor is None:
                return self._fallback_prediction(features_df, target_index, ratio_type)
            
            # 准备训练数据
            train_data = features_df.iloc[:target_index].copy()
            
            if len(train_data) < 20:  # 数据不足
                return self._fallback_prediction(features_df, target_index, ratio_type)
            
            # 根据比例类型选择目标列
            if ratio_type == 'red_odd_even':
                target_col = 'red_odd_count'
            elif ratio_type == 'red_size':
                target_col = 'red_big_count'
            elif ratio_type == 'blue_size':
                target_col = 'blue_big_count'
            else:
                return "2:3"  # 默认值
            
            # 训练模型
            train_result = predictor.train(train_data, target_col)
            
            if not train_result.get('error'):
                # 进行预测
                pred_result = predictor.predict(train_data, target_index)
                
                if pred_result.metadata.get('success'):
                    pred_value = pred_result.value
                    
                    # 将预测值转换为比例
                    if ratio_type == 'red_odd_even':
                        odd_count = max(0, min(5, round(pred_value)))
                        even_count = 5 - odd_count
                        return f"{odd_count}:{even_count}"
                    elif ratio_type == 'red_size':
                        big_count = max(0, min(5, round(pred_value)))
                        small_count = 5 - big_count
                        return f"{small_count}:{big_count}"
                    elif ratio_type == 'blue_size':
                        big_count = max(0, min(2, round(pred_value)))
                        small_count = 2 - big_count
                        return f"{small_count}:{big_count}"
            
            # 如果LSTM预测失败，使用回退方法
            return self._fallback_prediction(features_df, target_index, ratio_type)
            
        except Exception as e:
            self.logger.warning(f"LSTM预测失败: {e}，使用回退方法")
            return self._fallback_prediction(features_df, target_index, ratio_type)
    
    def _fallback_prediction(self, features_df: pd.DataFrame, target_index: int, ratio_type: str) -> str:
        """回退预测方法"""
        # 使用最近期数的统计方法
        recent_data = features_df.iloc[max(0, target_index-20):target_index]
        
        if ratio_type == 'red_odd_even':
            ratios = recent_data['red_odd_even_ratio'].value_counts()
        elif ratio_type == 'red_size':
            ratios = recent_data['red_size_ratio'].value_counts()
        elif ratio_type == 'blue_size':
            ratios = recent_data['blue_size_ratio'].value_counts()
        else:
            return "2:3"
        
        if len(ratios) > 0:
            return ratios.index[0]
        else:
            return "2:3"
    
    def lstm_kill_numbers(self, features_df: pd.DataFrame, target_index: int, ball_type: str) -> List[int]:
        """使用LSTM预测杀号"""
        try:
            predictor = self.create_lstm_predictor()
            if predictor is None:
                return self._fallback_kill_numbers(features_df, target_index, ball_type)
            
            train_data = features_df.iloc[:target_index].copy()
            
            if len(train_data) < 20:
                return self._fallback_kill_numbers(features_df, target_index, ball_type)
            
            # 根据球类型选择特征
            if ball_type == 'red':
                target_cols = ['red_sum', 'red_mean', 'red_std']
                max_num = 35
                kill_count = 3
            else:
                target_cols = ['blue_sum', 'blue_mean']
                max_num = 12
                kill_count = 1
            
            kill_candidates = []
            
            # 对每个特征进行预测
            for target_col in target_cols:
                train_result = predictor.train(train_data, target_col)
                
                if not train_result.get('error'):
                    pred_result = predictor.predict(train_data, target_index)
                    
                    if pred_result.metadata.get('success'):
                        pred_value = pred_result.value
                        
                        # 基于预测值生成杀号候选
                        if ball_type == 'red':
                            if target_col == 'red_sum':
                                # 基于和值预测杀号
                                avg_per_ball = pred_value / 5
                                candidates = [i for i in range(1, 36) if abs(i - avg_per_ball) > 10]
                            elif target_col == 'red_mean':
                                # 基于均值预测杀号
                                candidates = [i for i in range(1, 36) if abs(i - pred_value) > 8]
                            else:  # red_std
                                # 基于标准差预测杀号
                                if pred_value < 5:  # 低方差，杀掉极值
                                    candidates = list(range(1, 6)) + list(range(31, 36))
                                else:  # 高方差，杀掉中间值
                                    candidates = list(range(15, 21))
                        else:  # blue
                            if target_col == 'blue_sum':
                                avg_per_ball = pred_value / 2
                                candidates = [i for i in range(1, 13) if abs(i - avg_per_ball) > 3]
                            else:  # blue_mean
                                candidates = [i for i in range(1, 13) if abs(i - pred_value) > 2]
                        
                        kill_candidates.extend(candidates[:3])
            
            # 选择最频繁的杀号
            if kill_candidates:
                from collections import Counter
                kill_counts = Counter(kill_candidates)
                final_kills = [num for num, _ in kill_counts.most_common(kill_count)]
                return final_kills
            
            return self._fallback_kill_numbers(features_df, target_index, ball_type)
            
        except Exception as e:
            self.logger.warning(f"LSTM杀号失败: {e}，使用回退方法")
            return self._fallback_kill_numbers(features_df, target_index, ball_type)
    
    def _fallback_kill_numbers(self, features_df: pd.DataFrame, target_index: int, ball_type: str) -> List[int]:
        """回退杀号方法"""
        if ball_type == 'red':
            max_num = 35
            kill_count = 3
        else:
            max_num = 12
            kill_count = 1
        
        # 简单的频率杀号
        recent_data = features_df.iloc[max(0, target_index-10):target_index]
        
        if ball_type == 'red':
            # 基于红球统计特征杀号
            high_freq_ranges = []
            for _, row in recent_data.iterrows():
                if row['red_mean'] > 20:
                    high_freq_ranges.extend(range(1, 8))
                elif row['red_mean'] < 15:
                    high_freq_ranges.extend(range(28, 36))
        else:
            # 基于蓝球统计特征杀号
            high_freq_ranges = []
            for _, row in recent_data.iterrows():
                if row['blue_mean'] > 8:
                    high_freq_ranges.extend(range(1, 4))
                elif row['blue_mean'] < 5:
                    high_freq_ranges.extend(range(10, 13))
        
        if high_freq_ranges:
            from collections import Counter
            freq_counts = Counter(high_freq_ranges)
            return [num for num, _ in freq_counts.most_common(kill_count)]
        
        # 默认杀号
        if ball_type == 'red':
            return [1, 2, 35]
        else:
            return [12]
    
    def run_lstm_backtest(self, data: pd.DataFrame, test_periods: int = 10) -> Dict[str, Any]:
        """运行LSTM回测"""
        if len(data) < test_periods + 50:
            self.logger.error("数据不足进行回测")
            return {}
        
        self.logger.info(f"开始LSTM回测，测试期数: {test_periods}")
        
        # 准备特征数据
        features_df = self.prepare_lstm_features(data)
        
        results = {
            'period_details': [],
            'success_rates': {
                'red_odd_even': 0,
                'red_size': 0,
                'blue_size': 0,
                'red_kill': 0,
                'blue_kill': 0
            },
            'total_tests': 0
        }
        
        # 测试每个期数
        for i in range(test_periods):
            test_index = len(data) - test_periods + i
            actual_data = data.iloc[test_index]
            
            period_num = actual_data['期号']
            
            # 解析实际数据
            actual_red, actual_blue = self.parse_numbers(actual_data)
            actual_ratios = self.calculate_ratios(actual_red, actual_blue)
            
            if len(actual_red) < 5 or len(actual_blue) < 2:
                continue
            
            # LSTM预测
            pred_red_odd_even = self.lstm_ratio_prediction(features_df, test_index, 'red_odd_even')
            pred_red_size = self.lstm_ratio_prediction(features_df, test_index, 'red_size')
            pred_blue_size = self.lstm_ratio_prediction(features_df, test_index, 'blue_size')
            
            # LSTM杀号
            red_kills = self.lstm_kill_numbers(features_df, test_index, 'red')
            blue_kills = self.lstm_kill_numbers(features_df, test_index, 'blue')
            
            # 检查预测结果
            red_odd_even_hit = pred_red_odd_even == actual_ratios.get('red_odd_even', '')
            red_size_hit = pred_red_size == actual_ratios.get('red_size', '')
            blue_size_hit = pred_blue_size == actual_ratios.get('blue_size', '')
            
            red_kill_hit = all(num not in actual_red for num in red_kills)
            blue_kill_hit = all(num not in actual_blue for num in blue_kills)
            
            # 记录详细信息
            period_detail = {
                'period': period_num,
                'actual_red': actual_red,
                'actual_blue': actual_blue,
                'actual_ratios': actual_ratios,
                'predictions': {
                    'red_odd_even': pred_red_odd_even,
                    'red_size': pred_red_size,
                    'blue_size': pred_blue_size
                },
                'kills': {
                    'red_kills': red_kills,
                    'blue_kills': blue_kills
                },
                'results': {
                    'red_odd_even_hit': red_odd_even_hit,
                    'red_size_hit': red_size_hit,
                    'blue_size_hit': blue_size_hit,
                    'red_kill_hit': red_kill_hit,
                    'blue_kill_hit': blue_kill_hit
                }
            }
            
            results['period_details'].append(period_detail)
            
            # 统计成功率
            if red_odd_even_hit:
                results['success_rates']['red_odd_even'] += 1
            if red_size_hit:
                results['success_rates']['red_size'] += 1
            if blue_size_hit:
                results['success_rates']['blue_size'] += 1
            if red_kill_hit:
                results['success_rates']['red_kill'] += 1
            if blue_kill_hit:
                results['success_rates']['blue_kill'] += 1
            
            results['total_tests'] += 1
            
            # 输出进度
            print(f"期号 {period_num}:")
            print(f"  预测: 红奇偶{pred_red_odd_even} 红大小{pred_red_size} 蓝大小{pred_blue_size}")
            print(f"  实际: 红奇偶{actual_ratios.get('red_odd_even', '')} 红大小{actual_ratios.get('red_size', '')} 蓝大小{actual_ratios.get('blue_size', '')}")
            print(f"  杀号: 红球{red_kills} 蓝球{blue_kills}")
            print(f"  结果: 红奇偶{'OK' if red_odd_even_hit else 'NO'} 红大小{'OK' if red_size_hit else 'NO'} 蓝大小{'OK' if blue_size_hit else 'NO'} 红杀{'OK' if red_kill_hit else 'NO'} 蓝杀{'OK' if blue_kill_hit else 'NO'}")
            print()
        
        # 计算最终成功率
        if results['total_tests'] > 0:
            for key in results['success_rates']:
                results['success_rates'][key] = results['success_rates'][key] / results['total_tests']
        
        return results
    
    def generate_lstm_report(self, results: Dict[str, Any]) -> str:
        """生成LSTM回测报告"""
        report = []
        report.append("=" * 80)
        report.append("LSTM深度学习回测报告")
        report.append("=" * 80)
        
        if not results:
            report.append("回测失败或无数据")
            return "\n".join(report)
        
        # 总体统计
        total_tests = results['total_tests']
        success_rates = results['success_rates']
        
        report.append(f"\nLSTM回测统计 (共{total_tests}期):")
        report.append(f"   红球奇偶比成功率: {success_rates['red_odd_even']:.1%}")
        report.append(f"   红球大小比成功率: {success_rates['red_size']:.1%}")
        report.append(f"   蓝球大小比成功率: {success_rates['blue_size']:.1%}")
        report.append(f"   红球杀号成功率: {success_rates['red_kill']:.1%}")
        report.append(f"   蓝球杀号成功率: {success_rates['blue_kill']:.1%}")
        
        # 性能评估
        avg_success = np.mean(list(success_rates.values()))
        report.append(f"\n平均成功率: {avg_success:.1%}")
        
        if avg_success >= 0.7:
            report.append("LSTM模型表现优秀！")
        elif avg_success >= 0.5:
            report.append("LSTM模型表现良好")
        else:
            report.append("LSTM模型需要进一步优化")
        
        # 详细期数分析
        report.append(f"\n详细期数分析:")
        for detail in results['period_details']:
            period = detail['period']
            results_dict = detail['results']
            
            report.append(f"\n期号 {period}:")
            report.append(f"   实际: {detail['actual_ratios']}")
            report.append(f"   预测: {detail['predictions']}")
            report.append(f"   杀号: 红球{detail['kills']['red_kills']} 蓝球{detail['kills']['blue_kills']}")
            
            status_symbols = {
                'red_odd_even_hit': 'OK' if results_dict['red_odd_even_hit'] else 'NO',
                'red_size_hit': 'OK' if results_dict['red_size_hit'] else 'NO',
                'blue_size_hit': 'OK' if results_dict['blue_size_hit'] else 'NO',
                'red_kill_hit': 'OK' if results_dict['red_kill_hit'] else 'NO',
                'blue_kill_hit': 'OK' if results_dict['blue_kill_hit'] else 'NO'
            }
            
            report.append(f"   结果: 红奇偶{status_symbols['red_odd_even_hit']} 红大小{status_symbols['red_size_hit']} 蓝大小{status_symbols['blue_size_hit']} 红杀{status_symbols['red_kill_hit']} 蓝杀{status_symbols['blue_kill_hit']}")
        
        return "\n".join(report)


def main():
    """主函数"""
    print("LSTM深度学习回测系统")
    print("=" * 60)
    
    # 创建日志目录
    Path("logs").mkdir(exist_ok=True)
    
    system = LSTMBacktestSystem()
    
    # 加载数据
    print("加载数据...")
    data = system.load_data()
    
    if data.empty:
        print("数据加载失败")
        return 1
    
    # 运行LSTM回测
    print("运行LSTM回测...")
    start_time = time.time()
    
    results = system.run_lstm_backtest(data, test_periods=10)
    
    end_time = time.time()
    
    if results:
        print(f"LSTM回测完成，耗时 {end_time - start_time:.2f} 秒")
        
        # 生成报告
        report = system.generate_lstm_report(results)
        print(report)
        
        # 保存结果
        output_dir = Path("lstm_results")
        output_dir.mkdir(exist_ok=True)
        
        # 保存JSON格式的详细数据
        with open(output_dir / "lstm_backtest_results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存文本报告
        with open(output_dir / "lstm_backtest_report.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\nLSTM回测结果已保存到 {output_dir}")
        print("   - lstm_backtest_results.json: 完整回测数据")
        print("   - lstm_backtest_report.txt: 文本格式报告")
    
    return 0

if __name__ == "__main__":
    exit(main())