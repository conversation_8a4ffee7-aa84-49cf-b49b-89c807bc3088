"""
统一杀号算法模块
整合所有杀号策略，提供统一的接口
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from collections import Counter, defaultdict

from .utils import parse_numbers


class UnifiedKillAlgorithms:
    """统一杀号算法类"""
    
    def __init__(self):
        """初始化杀号算法"""
        self.red_range = list(range(1, 36))  # 红球范围1-35
        self.blue_range = list(range(1, 13))  # 蓝球范围1-12
        self.historical_stats = {}
        
        # 杀号历史记录
        self.red_kill_history = []
        self.blue_kill_history = []
        self.red_success_history = []
        self.blue_success_history = []
    
    def generate_red_kill_numbers(self, data: pd.DataFrame, period_num: int, 
                                 kill_count: int = 13) -> List[int]:
        """
        生成红球杀号
        
        Args:
            data: 历史数据
            period_num: 当前期号
            kill_count: 杀号数量
            
        Returns:
            List[int]: 红球杀号列表
        """
        if len(data) < 2:
            return list(range(1, kill_count + 1))
        
        # 获取最近两期数据
        recent_data = data.tail(2)
        prev_two_periods = []
        
        for _, row in recent_data.iterrows():
            red_balls, _ = parse_numbers(row)
            prev_two_periods.append(red_balls)
        
        # 使用多种策略生成杀号
        kill_candidates = set()
        
        # 策略1: 频率分析杀号
        freq_kills = self._frequency_kill_strategy(data, 'red', kill_count // 3)
        kill_candidates.update(freq_kills)
        
        # 策略2: 连号分析杀号
        consecutive_kills = self._consecutive_kill_strategy(prev_two_periods, kill_count // 3)
        kill_candidates.update(consecutive_kills)
        
        # 策略3: 和值分析杀号
        sum_kills = self._sum_analysis_kill_strategy(prev_two_periods, kill_count // 3)
        kill_candidates.update(sum_kills)
        
        # 策略4: 奇偶分析杀号
        parity_kills = self._parity_kill_strategy(prev_two_periods, kill_count // 4)
        kill_candidates.update(parity_kills)
        
        # 确保杀号数量
        kill_list = list(kill_candidates)[:kill_count]
        
        # 如果不够，补充一些低频号码
        if len(kill_list) < kill_count:
            all_numbers = set(range(1, 36))
            remaining = all_numbers - kill_candidates
            kill_list.extend(list(remaining)[:kill_count - len(kill_list)])
        
        return sorted(kill_list[:kill_count])
    
    def generate_blue_kill_numbers(self, data: pd.DataFrame, period_num: int,
                                  kill_count: int = 2) -> List[int]:
        """
        生成蓝球杀号
        
        Args:
            data: 历史数据
            period_num: 当前期号
            kill_count: 杀号数量
            
        Returns:
            List[int]: 蓝球杀号列表
        """
        if len(data) < 2:
            return [1, 2]
        
        # 获取最近两期数据
        recent_data = data.tail(2)
        prev_two_periods = []
        
        for _, row in recent_data.iterrows():
            _, blue_balls = parse_numbers(row)
            prev_two_periods.append(blue_balls)
        
        # 使用多种策略生成杀号
        kill_candidates = set()
        
        # 策略1: 频率分析杀号
        freq_kills = self._frequency_kill_strategy(data, 'blue', kill_count)
        kill_candidates.update(freq_kills)
        
        # 策略2: 连号分析杀号
        consecutive_kills = self._consecutive_blue_kill_strategy(prev_two_periods, kill_count)
        kill_candidates.update(consecutive_kills)
        
        # 确保杀号数量
        kill_list = list(kill_candidates)[:kill_count]
        
        # 如果不够，补充一些号码
        if len(kill_list) < kill_count:
            all_blues = set(range(1, 13))
            remaining = all_blues - kill_candidates
            kill_list.extend(list(remaining)[:kill_count - len(kill_list)])
        
        return sorted(kill_list[:kill_count])
    
    def _frequency_kill_strategy(self, data: pd.DataFrame, ball_type: str, 
                               count: int) -> List[int]:
        """频率分析杀号策略"""
        numbers = []
        
        # 收集最近50期的号码
        recent_data = data.tail(50)
        for _, row in recent_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            if ball_type == 'red':
                numbers.extend(red_balls)
            else:
                numbers.extend(blue_balls)
        
        # 统计频率，选择最少出现的号码
        number_range = self.red_range if ball_type == 'red' else self.blue_range
        freq_counter = Counter(numbers)
        
        # 找出最少出现的号码
        min_freq = min(freq_counter.get(n, 0) for n in number_range)
        candidates = [n for n in number_range if freq_counter.get(n, 0) == min_freq]
        
        return candidates[:count]
    
    def _consecutive_kill_strategy(self, prev_periods: List[List[int]], 
                                 count: int) -> List[int]:
        """连号分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 分析连号模式
        all_numbers = []
        for period in prev_periods:
            all_numbers.extend(period)
        
        # 找出连续出现的号码模式，避免这些号码
        consecutive_patterns = []
        sorted_numbers = sorted(set(all_numbers))
        
        for i in range(len(sorted_numbers) - 1):
            if sorted_numbers[i+1] - sorted_numbers[i] == 1:
                consecutive_patterns.extend([sorted_numbers[i], sorted_numbers[i+1]])
        
        # 选择一些连号作为杀号候选
        return list(set(consecutive_patterns))[:count]
    
    def _sum_analysis_kill_strategy(self, prev_periods: List[List[int]], 
                                  count: int) -> List[int]:
        """和值分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 计算最近期的和值
        recent_sums = [sum(period) for period in prev_periods]
        avg_sum = sum(recent_sums) / len(recent_sums)
        
        # 根据和值趋势，杀掉一些极端号码
        if avg_sum < 80:  # 和值偏小，杀大号
            return list(range(30, 36))[:count]
        elif avg_sum > 120:  # 和值偏大，杀小号
            return list(range(1, 7))[:count]
        else:
            return []
    
    def _parity_kill_strategy(self, prev_periods: List[List[int]], 
                            count: int) -> List[int]:
        """奇偶分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 分析奇偶比例
        odd_counts = []
        for period in prev_periods:
            odd_count = sum(1 for n in period if n % 2 == 1)
            odd_counts.append(odd_count)
        
        avg_odd = sum(odd_counts) / len(odd_counts)
        
        # 根据奇偶趋势杀号
        if avg_odd > 3:  # 奇数偏多，杀一些奇数
            odd_numbers = [n for n in range(1, 36) if n % 2 == 1]
            return odd_numbers[:count]
        elif avg_odd < 2:  # 偶数偏多，杀一些偶数
            even_numbers = [n for n in range(1, 36) if n % 2 == 0]
            return even_numbers[:count]
        else:
            return []
    
    def _consecutive_blue_kill_strategy(self, prev_periods: List[List[int]], 
                                      count: int) -> List[int]:
        """蓝球连号分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 收集最近的蓝球号码
        all_blues = []
        for period in prev_periods:
            all_blues.extend(period)
        
        # 统计频率，选择高频号码作为杀号候选
        freq_counter = Counter(all_blues)
        most_common = freq_counter.most_common(count)
        
        return [num for num, _ in most_common]


# 向后兼容的类别名
NumberKiller = UnifiedKillAlgorithms
UniversalKiller = UnifiedKillAlgorithms
BlueKiller = UnifiedKillAlgorithms
