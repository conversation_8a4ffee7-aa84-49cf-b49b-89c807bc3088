#!/usr/bin/env python3
"""
改进算法
Improved Algorithm

基于失败分析结果的改进预测算法
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Any
from collections import Counter, defaultdict
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/improved_algorithm.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ImprovedPredictor:
    """改进的预测器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"improved_algorithm.{self.__class__.__name__}")
        
    def parse_numbers(self, row: pd.Series) -> <PERSON><PERSON>[List[int], List[int]]:
        """解析红球和蓝球数字"""
        red_numbers = []
        for i in range(1, 6):  # 红球1到红球5
            col_name = f'红球{i}'
            if col_name in row and pd.notna(row[col_name]):
                red_numbers.append(int(row[col_name]))
        
        blue_numbers = []
        for i in range(1, 3):  # 蓝球1到蓝球2
            col_name = f'蓝球{i}'
            if col_name in row and pd.notna(row[col_name]):
                blue_numbers.append(int(row[col_name]))
        
        return red_numbers, blue_numbers
    
    def calculate_ratios(self, red_numbers: List[int], blue_numbers: List[int]) -> Dict[str, str]:
        """计算各种比例"""
        ratios = {}
        
        if len(red_numbers) >= 5:
            # 红球奇偶比
            odd_count = sum(1 for num in red_numbers if num % 2 == 1)
            even_count = 5 - odd_count
            ratios['red_odd_even'] = f"{odd_count}:{even_count}"
            
            # 红球大小比
            big_count = sum(1 for num in red_numbers if num > 17)
            small_count = 5 - big_count
            ratios['red_size'] = f"{small_count}:{big_count}"
        
        if len(blue_numbers) >= 2:
            # 蓝球大小比
            big_count = sum(1 for num in blue_numbers if num > 6)
            small_count = 2 - big_count
            ratios['blue_size'] = f"{small_count}:{big_count}"
        
        return ratios
    
    def advanced_pattern_prediction(self, historical_patterns: List[str], pattern_type: str) -> str:
        """高级模式预测"""
        if len(historical_patterns) < 10:
            return self.get_default_pattern(pattern_type)
        
        # 多窗口分析
        windows = [5, 10, 20, 30]
        predictions = []
        
        for window in windows:
            if len(historical_patterns) >= window:
                recent_patterns = historical_patterns[-window:]
                pattern_counts = Counter(recent_patterns)
                
                if pattern_counts:
                    # 加权预测：考虑频率和趋势
                    weighted_scores = {}
                    for pattern, count in pattern_counts.items():
                        # 基础频率分数
                        freq_score = count / len(recent_patterns)
                        
                        # 趋势分数：最近出现的模式权重更高
                        trend_score = 0
                        for i, p in enumerate(reversed(recent_patterns[:5])):  # 最近5期
                            if p == pattern:
                                trend_score += (5 - i) / 15  # 权重递减
                        
                        # 组合分数
                        weighted_scores[pattern] = freq_score * 0.7 + trend_score * 0.3
                    
                    best_pattern = max(weighted_scores.items(), key=lambda x: x[1])[0]
                    predictions.append((best_pattern, weighted_scores[best_pattern]))
        
        if predictions:
            # 选择得分最高的预测
            best_prediction = max(predictions, key=lambda x: x[1])[0]
            return best_prediction
        
        return self.get_default_pattern(pattern_type)
    
    def get_default_pattern(self, pattern_type: str) -> str:
        """获取默认模式"""
        defaults = {
            'red_odd_even': '2:3',  # 从分析中发现2:3更常见
            'red_size': '2:3',      # 从分析中发现2:3更常见
            'blue_size': '1:1'      # 保持1:1作为默认
        }
        return defaults.get(pattern_type, '2:3')
    
    def smart_kill_strategy(self, data: pd.DataFrame, ball_type: str) -> List[int]:
        """智能杀号策略"""
        if ball_type == 'red':
            max_num = 35
            kill_count = 3  # 减少杀号数量，提高准确性
        else:
            max_num = 12
            kill_count = 1  # 蓝球只杀1个
        
        # 多策略组合
        strategies = [
            self._frequency_based_kill,
            self._gap_based_kill,
            self._pattern_based_kill
        ]
        
        all_candidates = []
        for strategy in strategies:
            candidates = strategy(data, ball_type, max_num)
            all_candidates.extend(candidates)
        
        # 投票机制：选择被多个策略推荐的数字
        candidate_votes = Counter(all_candidates)
        
        # 优先选择得票多的数字
        final_kills = []
        for num, votes in candidate_votes.most_common():
            if len(final_kills) < kill_count and votes >= 2:  # 至少2个策略支持
                final_kills.append(num)
        
        # 如果不够，补充得票最多的
        if len(final_kills) < kill_count:
            remaining = [num for num, _ in candidate_votes.most_common() 
                        if num not in final_kills]
            final_kills.extend(remaining[:kill_count - len(final_kills)])
        
        return sorted(final_kills[:kill_count])
    
    def _frequency_based_kill(self, data: pd.DataFrame, ball_type: str, max_num: int) -> List[int]:
        """基于频率的杀号"""
        frequency = defaultdict(int)
        recent_data = data.tail(20)  # 减少到20期
        
        for _, row in recent_data.iterrows():
            numbers = []
            if ball_type == 'red':
                for i in range(1, 6):
                    col_name = f'红球{i}'
                    if col_name in row and pd.notna(row[col_name]):
                        numbers.append(int(row[col_name]))
            else:
                for i in range(1, 3):
                    col_name = f'蓝球{i}'
                    if col_name in row and pd.notna(row[col_name]):
                        numbers.append(int(row[col_name]))
            
            for num in numbers:
                if 1 <= num <= max_num:
                    frequency[num] += 1
        
        # 杀掉出现频率最高的数字（热号冷却理论）
        sorted_freq = sorted(frequency.items(), key=lambda x: x[1], reverse=True)
        return [num for num, _ in sorted_freq[:max_num//6]]
    
    def _gap_based_kill(self, data: pd.DataFrame, ball_type: str, max_num: int) -> List[int]:
        """基于间隔的杀号"""
        last_appearance = {}
        recent_data = data.tail(15)
        
        for idx, (_, row) in enumerate(recent_data.iterrows()):
            numbers = []
            if ball_type == 'red':
                for i in range(1, 6):
                    col_name = f'红球{i}'
                    if col_name in row and pd.notna(row[col_name]):
                        numbers.append(int(row[col_name]))
            else:
                for i in range(1, 3):
                    col_name = f'蓝球{i}'
                    if col_name in row and pd.notna(row[col_name]):
                        numbers.append(int(row[col_name]))
            
            for num in numbers:
                if 1 <= num <= max_num:
                    last_appearance[num] = idx
        
        # 杀掉最近3期内出现的数字
        recent_numbers = [num for num, idx in last_appearance.items() 
                         if idx >= len(recent_data) - 3]
        return recent_numbers[:max_num//8]
    
    def _pattern_based_kill(self, data: pd.DataFrame, ball_type: str, max_num: int) -> List[int]:
        """基于模式的杀号"""
        # 分析连号模式
        consecutive_pairs = defaultdict(int)
        recent_data = data.tail(10)
        
        for _, row in recent_data.iterrows():
            numbers = []
            if ball_type == 'red':
                for i in range(1, 6):
                    col_name = f'红球{i}'
                    if col_name in row and pd.notna(row[col_name]):
                        numbers.append(int(row[col_name]))
            else:
                for i in range(1, 3):
                    col_name = f'蓝球{i}'
                    if col_name in row and pd.notna(row[col_name]):
                        numbers.append(int(row[col_name]))
            
            numbers.sort()
            # 找连号
            for i in range(len(numbers) - 1):
                if numbers[i+1] - numbers[i] == 1:
                    consecutive_pairs[numbers[i]] += 1
                    consecutive_pairs[numbers[i+1]] += 1
        
        # 杀掉经常连号的数字
        sorted_consecutive = sorted(consecutive_pairs.items(), key=lambda x: x[1], reverse=True)
        return [num for num, _ in sorted_consecutive[:max_num//10]]


class ImprovedSystem:
    """改进的系统"""
    
    def __init__(self):
        self.predictor = ImprovedPredictor()
        self.logger = logging.getLogger(f"improved_algorithm.{self.__class__.__name__}")
    
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        data_path = Path("data/raw/dlt_data.csv")
        if data_path.exists():
            data = pd.read_csv(data_path)
            self.logger.info(f"加载数据: {len(data)} 条记录")
            return data
        else:
            self.logger.error("数据文件不存在")
            return pd.DataFrame()
    
    def backtest_improved_algorithm(self, data: pd.DataFrame, test_periods: int = 10) -> Dict[str, float]:
        """回测改进算法"""
        if len(data) < test_periods + 50:
            self.logger.error("数据不足进行回测")
            return {}
        
        results = {
            'red_odd_even_hits': 0,
            'red_size_hits': 0,
            'blue_size_hits': 0,
            'red_kill_success': 0,
            'blue_kill_success': 0,
            'total_tests': 0
        }
        
        # 收集历史模式
        historical_data = data.iloc[:-test_periods]
        red_odd_even_patterns = []
        red_size_patterns = []
        blue_size_patterns = []
        
        for _, row in historical_data.iterrows():
            red_numbers, blue_numbers = self.predictor.parse_numbers(row)
            ratios = self.predictor.calculate_ratios(red_numbers, blue_numbers)
            
            if 'red_odd_even' in ratios:
                red_odd_even_patterns.append(ratios['red_odd_even'])
            if 'red_size' in ratios:
                red_size_patterns.append(ratios['red_size'])
            if 'blue_size' in ratios:
                blue_size_patterns.append(ratios['blue_size'])
        
        # 测试每个期数
        for i in range(test_periods):
            test_index = len(data) - test_periods + i
            train_data = data.iloc[:test_index]
            actual_data = data.iloc[test_index]
            
            # 解析实际数据
            actual_red, actual_blue = self.predictor.parse_numbers(actual_data)
            actual_ratios = self.predictor.calculate_ratios(actual_red, actual_blue)
            
            if len(actual_red) < 5 or len(actual_blue) < 2:
                continue
            
            # 使用改进算法预测
            pred_red_odd_even = self.predictor.advanced_pattern_prediction(
                red_odd_even_patterns[-50:], 'red_odd_even')
            pred_red_size = self.predictor.advanced_pattern_prediction(
                red_size_patterns[-50:], 'red_size')
            pred_blue_size = self.predictor.advanced_pattern_prediction(
                blue_size_patterns[-50:], 'blue_size')
            
            # 智能杀号
            red_kills = self.predictor.smart_kill_strategy(train_data, 'red')
            blue_kills = self.predictor.smart_kill_strategy(train_data, 'blue')
            
            # 检查预测结果
            red_odd_even_hit = pred_red_odd_even == actual_ratios.get('red_odd_even', '')
            red_size_hit = pred_red_size == actual_ratios.get('red_size', '')
            blue_size_hit = pred_blue_size == actual_ratios.get('blue_size', '')
            
            red_kill_hit = all(num not in actual_red for num in red_kills)
            blue_kill_hit = all(num not in actual_blue for num in blue_kills)
            
            # 统计结果
            if red_odd_even_hit:
                results['red_odd_even_hits'] += 1
            if red_size_hit:
                results['red_size_hits'] += 1
            if blue_size_hit:
                results['blue_size_hits'] += 1
            if red_kill_hit:
                results['red_kill_success'] += 1
            if blue_kill_hit:
                results['blue_kill_success'] += 1
            
            results['total_tests'] += 1
            
            # 输出详细结果
            period_num = actual_data['期号']
            print(f"期号 {period_num}:")
            print(f"  预测: 红奇偶{pred_red_odd_even} 红大小{pred_red_size} 蓝大小{pred_blue_size}")
            print(f"  实际: 红奇偶{actual_ratios.get('red_odd_even', '')} 红大小{actual_ratios.get('red_size', '')} 蓝大小{actual_ratios.get('blue_size', '')}")
            print(f"  杀号: 红球{red_kills} 蓝球{blue_kills}")
            print(f"  结果: 红奇偶{'✅' if red_odd_even_hit else '❌'} 红大小{'✅' if red_size_hit else '❌'} 蓝大小{'✅' if blue_size_hit else '❌'} 红杀{'✅' if red_kill_hit else '❌'} 蓝杀{'✅' if blue_kill_hit else '❌'}")
            print()
        
        # 计算成功率
        if results['total_tests'] > 0:
            success_rates = {
                'red_odd_even_rate': results['red_odd_even_hits'] / results['total_tests'],
                'red_size_rate': results['red_size_hits'] / results['total_tests'],
                'blue_size_rate': results['blue_size_hits'] / results['total_tests'],
                'red_kill_rate': results['red_kill_success'] / results['total_tests'],
                'blue_kill_rate': results['blue_kill_success'] / results['total_tests']
            }
        else:
            success_rates = {}
        
        return success_rates


def main():
    """主函数"""
    print("🚀 改进算法测试系统")
    print("=" * 60)
    
    # 创建日志目录
    Path("logs").mkdir(exist_ok=True)
    
    system = ImprovedSystem()
    
    # 加载数据
    print("📊 加载数据...")
    data = system.load_data()
    
    if data.empty:
        print("❌ 数据加载失败")
        return 1
    
    # 回测改进算法
    print("🧪 回测改进算法...")
    start_time = time.time()
    
    success_rates = system.backtest_improved_algorithm(data, test_periods=10)
    
    end_time = time.time()
    
    if success_rates:
        print(f"✅ 回测完成，耗时 {end_time - start_time:.2f} 秒")
        print("\n📈 改进算法成功率:")
        print(f"   红球奇偶比命中率: {success_rates['red_odd_even_rate']:.1%}")
        print(f"   红球大小比命中率: {success_rates['red_size_rate']:.1%}")
        print(f"   蓝球大小比命中率: {success_rates['blue_size_rate']:.1%}")
        print(f"   红球杀号成功率: {success_rates['red_kill_rate']:.1%}")
        print(f"   蓝球杀号成功率: {success_rates['blue_kill_rate']:.1%}")
        
        # 检查改进效果
        targets_met = 0
        total_targets = 5
        
        if success_rates['red_odd_even_rate'] >= 0.7:
            targets_met += 1
            print("   ✅ 红球奇偶比达到目标 (≥70%)")
        else:
            print(f"   ⚠️ 红球奇偶比需继续优化 ({success_rates['red_odd_even_rate']:.1%})")
            
        if success_rates['red_size_rate'] >= 0.7:
            targets_met += 1
            print("   ✅ 红球大小比达到目标 (≥70%)")
        else:
            print(f"   ⚠️ 红球大小比需继续优化 ({success_rates['red_size_rate']:.1%})")
            
        if success_rates['blue_size_rate'] >= 0.7:
            targets_met += 1
            print("   ✅ 蓝球大小比达到目标 (≥70%)")
        else:
            print(f"   ⚠️ 蓝球大小比需继续优化 ({success_rates['blue_size_rate']:.1%})")
            
        if success_rates['red_kill_rate'] >= 0.9:
            targets_met += 1
            print("   ✅ 红球杀号达到目标 (≥90%)")
        else:
            print(f"   ⚠️ 红球杀号需继续优化 ({success_rates['red_kill_rate']:.1%})")
            
        if success_rates['blue_kill_rate'] >= 0.9:
            targets_met += 1
            print("   ✅ 蓝球杀号达到目标 (≥90%)")
        else:
            print(f"   ⚠️ 蓝球杀号需继续优化 ({success_rates['blue_kill_rate']:.1%})")
        
        print(f"\n🎯 目标达成率: {targets_met}/{total_targets} ({targets_met/total_targets:.1%})")
        
        if targets_met >= 3:
            print("🎉 算法改进效果显著！")
        else:
            print("🔧 需要进一步调优")
    
    return 0

if __name__ == "__main__":
    exit(main())
