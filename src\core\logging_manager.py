"""统一日志管理系统

提供日志记录、格式化、输出和性能监控功能。
"""

import os
import sys
import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Callable, TextIO
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from pathlib import Path
from functools import wraps
import traceback
import queue
import gzip
import shutil
from concurrent.futures import ThreadPoolExecutor

from .exceptions import SystemException, ErrorCode


class LogLevel(Enum):
    """日志级别"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class LogFormat(Enum):
    """日志格式"""
    SIMPLE = 'simple'
    DETAILED = 'detailed'
    JSON = 'json'
    CUSTOM = 'custom'


class OutputType(Enum):
    """输出类型"""
    CONSOLE = 'console'
    FILE = 'file'
    ROTATING_FILE = 'rotating_file'
    TIMED_ROTATING_FILE = 'timed_rotating_file'
    SYSLOG = 'syslog'
    NETWORK = 'network'
    DATABASE = 'database'
    MEMORY = 'memory'


class RotationPolicy(Enum):
    """轮转策略"""
    SIZE = 'size'
    TIME = 'time'
    COUNT = 'count'


@dataclass
class LogConfig:
    """日志配置"""
    name: str
    level: LogLevel = LogLevel.INFO
    format: LogFormat = LogFormat.DETAILED
    output_type: OutputType = OutputType.CONSOLE
    
    # 文件输出配置
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    encoding: str = 'utf-8'
    
    # 轮转配置
    rotation_policy: RotationPolicy = RotationPolicy.SIZE
    rotation_interval: str = 'midnight'  # 时间轮转间隔
    
    # 网络输出配置
    host: Optional[str] = None
    port: Optional[int] = None
    
    # 过滤配置
    filters: List[str] = field(default_factory=list)
    
    # 自定义格式
    custom_format: Optional[str] = None
    date_format: str = '%Y-%m-%d %H:%M:%S'
    
    # 性能配置
    async_logging: bool = False
    buffer_size: int = 1000
    flush_interval: float = 1.0
    
    # 其他配置
    propagate: bool = True
    capture_warnings: bool = True
    include_caller_info: bool = True
    include_thread_info: bool = True
    include_process_info: bool = True


@dataclass
class LogRecord:
    """日志记录"""
    timestamp: datetime
    level: LogLevel
    logger_name: str
    message: str
    
    # 上下文信息
    module: Optional[str] = None
    function: Optional[str] = None
    line_number: Optional[int] = None
    thread_id: Optional[int] = None
    thread_name: Optional[str] = None
    process_id: Optional[int] = None
    process_name: Optional[str] = None
    
    # 异常信息
    exception_type: Optional[str] = None
    exception_message: Optional[str] = None
    exception_traceback: Optional[str] = None
    
    # 自定义字段
    extra_fields: Dict[str, Any] = field(default_factory=dict)
    
    # 性能信息
    execution_time: Optional[float] = None
    memory_usage: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        data = {
            'timestamp': self.timestamp.isoformat(),
            'level': self.level.name,
            'logger': self.logger_name,
            'message': self.message
        }
        
        # 添加上下文信息
        if self.module:
            data['module'] = self.module
        if self.function:
            data['function'] = self.function
        if self.line_number:
            data['line'] = self.line_number
        if self.thread_id:
            data['thread_id'] = self.thread_id
        if self.thread_name:
            data['thread_name'] = self.thread_name
        if self.process_id:
            data['process_id'] = self.process_id
        if self.process_name:
            data['process_name'] = self.process_name
        
        # 添加异常信息
        if self.exception_type:
            data['exception'] = {
                'type': self.exception_type,
                'message': self.exception_message,
                'traceback': self.exception_traceback
            }
        
        # 添加性能信息
        if self.execution_time is not None:
            data['execution_time'] = self.execution_time
        if self.memory_usage is not None:
            data['memory_usage'] = self.memory_usage
        
        # 添加自定义字段
        data.update(self.extra_fields)
        
        return data


class LogFormatter:
    """日志格式化器"""
    
    def __init__(self, config: LogConfig):
        self.config = config
        self.format_map = {
            LogFormat.SIMPLE: self._format_simple,
            LogFormat.DETAILED: self._format_detailed,
            LogFormat.JSON: self._format_json,
            LogFormat.CUSTOM: self._format_custom
        }
    
    def format(self, record: LogRecord) -> str:
        """格式化日志记录
        
        Args:
            record: 日志记录
        
        Returns:
            str: 格式化后的日志
        """
        formatter = self.format_map.get(self.config.format, self._format_detailed)
        return formatter(record)
    
    def _format_simple(self, record: LogRecord) -> str:
        """简单格式
        
        Args:
            record: 日志记录
        
        Returns:
            str: 格式化后的日志
        """
        timestamp = record.timestamp.strftime(self.config.date_format)
        return f"{timestamp} [{record.level.name}] {record.message}"
    
    def _format_detailed(self, record: LogRecord) -> str:
        """详细格式
        
        Args:
            record: 日志记录
        
        Returns:
            str: 格式化后的日志
        """
        timestamp = record.timestamp.strftime(self.config.date_format)
        
        # 基本信息
        parts = [
            timestamp,
            f"[{record.level.name}]",
            f"[{record.logger_name}]"
        ]
        
        # 添加位置信息
        if record.module and record.function:
            location = f"{record.module}.{record.function}"
            if record.line_number:
                location += f":{record.line_number}"
            parts.append(f"[{location}]")
        
        # 添加线程信息
        if self.config.include_thread_info and record.thread_name:
            parts.append(f"[{record.thread_name}]")
        
        # 添加进程信息
        if self.config.include_process_info and record.process_name:
            parts.append(f"[{record.process_name}]")
        
        # 添加消息
        parts.append(record.message)
        
        # 添加异常信息
        if record.exception_traceback:
            parts.append(f"\n{record.exception_traceback}")
        
        return ' '.join(parts)
    
    def _format_json(self, record: LogRecord) -> str:
        """JSON格式
        
        Args:
            record: 日志记录
        
        Returns:
            str: 格式化后的日志
        """
        return json.dumps(record.to_dict(), ensure_ascii=False)
    
    def _format_custom(self, record: LogRecord) -> str:
        """自定义格式
        
        Args:
            record: 日志记录
        
        Returns:
            str: 格式化后的日志
        """
        if not self.config.custom_format:
            return self._format_detailed(record)
        
        # 创建格式化变量
        format_vars = {
            'timestamp': record.timestamp.strftime(self.config.date_format),
            'level': record.level.name,
            'logger': record.logger_name,
            'message': record.message,
            'module': record.module or '',
            'function': record.function or '',
            'line': record.line_number or '',
            'thread_id': record.thread_id or '',
            'thread_name': record.thread_name or '',
            'process_id': record.process_id or '',
            'process_name': record.process_name or ''
        }
        
        # 添加自定义字段
        format_vars.update(record.extra_fields)
        
        try:
            return self.config.custom_format.format(**format_vars)
        except (KeyError, ValueError):
            return self._format_detailed(record)


class LogOutput:
    """日志输出器抽象基类"""
    
    def __init__(self, config: LogConfig):
        self.config = config
        self.formatter = LogFormatter(config)
        self._lock = threading.RLock()
    
    def write(self, record: LogRecord):
        """写入日志记录
        
        Args:
            record: 日志记录
        """
        raise NotImplementedError
    
    def flush(self):
        """刷新输出"""
        pass
    
    def close(self):
        """关闭输出"""
        pass


class ConsoleOutput(LogOutput):
    """控制台输出"""
    
    def __init__(self, config: LogConfig):
        super().__init__(config)
        self.stream = sys.stdout
    
    def write(self, record: LogRecord):
        """写入控制台
        
        Args:
            record: 日志记录
        """
        with self._lock:
            formatted_message = self.formatter.format(record)
            
            # 错误级别输出到stderr
            if record.level.value >= LogLevel.ERROR.value:
                stream = sys.stderr
            else:
                stream = self.stream
            
            stream.write(formatted_message + '\n')
            stream.flush()


class FileOutput(LogOutput):
    """文件输出"""
    
    def __init__(self, config: LogConfig):
        super().__init__(config)
        
        if not config.file_path:
            raise ValueError("文件路径不能为空")
        
        self.file_path = Path(config.file_path)
        self.file_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.file_handle: Optional[TextIO] = None
        self._open_file()
    
    def _open_file(self):
        """打开文件"""
        try:
            self.file_handle = open(
                self.file_path,
                'a',
                encoding=self.config.encoding,
                buffering=1  # 行缓冲
            )
        except Exception as e:
            raise SystemException(f"无法打开日志文件 {self.file_path}: {e}", ErrorCode.FILE_WRITE_ERROR)
    
    def write(self, record: LogRecord):
        """写入文件
        
        Args:
            record: 日志记录
        """
        if not self.file_handle:
            self._open_file()
        
        with self._lock:
            formatted_message = self.formatter.format(record)
            self.file_handle.write(formatted_message + '\n')
    
    def flush(self):
        """刷新文件"""
        if self.file_handle:
            self.file_handle.flush()
    
    def close(self):
        """关闭文件"""
        if self.file_handle:
            self.file_handle.close()
            self.file_handle = None


class MemoryOutput(LogOutput):
    """内存输出"""
    
    def __init__(self, config: LogConfig):
        super().__init__(config)
        self.records: List[LogRecord] = []
        self.max_records = config.buffer_size
    
    def write(self, record: LogRecord):
        """写入内存
        
        Args:
            record: 日志记录
        """
        with self._lock:
            self.records.append(record)
            
            # 保持最大记录数
            if len(self.records) > self.max_records:
                self.records.pop(0)
    
    def get_records(self, level: Optional[LogLevel] = None, limit: Optional[int] = None) -> List[LogRecord]:
        """获取日志记录
        
        Args:
            level: 日志级别过滤
            limit: 限制数量
        
        Returns:
            List[LogRecord]: 日志记录列表
        """
        with self._lock:
            records = self.records.copy()
            
            # 级别过滤
            if level:
                records = [r for r in records if r.level.value >= level.value]
            
            # 限制数量
            if limit:
                records = records[-limit:]
            
            return records
    
    def clear(self):
        """清空记录"""
        with self._lock:
            self.records.clear()


class Logger:
    """日志记录器"""
    
    def __init__(self, name: str, config: LogConfig):
        self.name = name
        self.config = config
        self.outputs: List[LogOutput] = []
        self.filters: List[Callable[[LogRecord], bool]] = []
        
        # 创建输出器
        self._create_outputs()
        
        # 性能统计
        self.stats = {
            'total_logs': 0,
            'logs_by_level': {level.name: 0 for level in LogLevel},
            'errors': 0,
            'start_time': datetime.now()
        }
    
    def _create_outputs(self):
        """创建输出器"""
        output_map = {
            OutputType.CONSOLE: ConsoleOutput,
            OutputType.FILE: FileOutput,
            OutputType.MEMORY: MemoryOutput
        }
        
        output_class = output_map.get(self.config.output_type)
        if not output_class:
            raise ValueError(f"不支持的输出类型: {self.config.output_type}")
        
        output = output_class(self.config)
        self.outputs.append(output)
    
    def add_filter(self, filter_func: Callable[[LogRecord], bool]):
        """添加过滤器
        
        Args:
            filter_func: 过滤函数，返回True表示通过
        """
        self.filters.append(filter_func)
    
    def log(self, level: LogLevel, message: str, **kwargs):
        """记录日志
        
        Args:
            level: 日志级别
            message: 日志消息
            **kwargs: 额外字段
        """
        # 检查日志级别
        if level.value < self.config.level.value:
            return
        
        # 创建日志记录
        record = self._create_record(level, message, **kwargs)
        
        # 应用过滤器
        for filter_func in self.filters:
            if not filter_func(record):
                return
        
        # 输出日志
        for output in self.outputs:
            try:
                output.write(record)
            except Exception:
                self.stats['errors'] += 1
        
        # 更新统计
        self.stats['total_logs'] += 1
        self.stats['logs_by_level'][level.name] += 1
    
    def _create_record(self, level: LogLevel, message: str, **kwargs) -> LogRecord:
        """创建日志记录
        
        Args:
            level: 日志级别
            message: 日志消息
            **kwargs: 额外字段
        
        Returns:
            LogRecord: 日志记录
        """
        # 获取调用者信息
        frame = sys._getframe(3)  # 跳过log、debug/info/warning/error、_create_record
        
        record = LogRecord(
            timestamp=datetime.now(),
            level=level,
            logger_name=self.name,
            message=message
        )
        
        # 添加调用者信息
        if self.config.include_caller_info:
            record.module = frame.f_globals.get('__name__')
            record.function = frame.f_code.co_name
            record.line_number = frame.f_lineno
        
        # 添加线程信息
        if self.config.include_thread_info:
            current_thread = threading.current_thread()
            record.thread_id = current_thread.ident
            record.thread_name = current_thread.name
        
        # 添加进程信息
        if self.config.include_process_info:
            record.process_id = os.getpid()
            record.process_name = os.path.basename(sys.argv[0]) if sys.argv else 'python'
        
        # 添加异常信息
        if 'exc_info' in kwargs and kwargs['exc_info']:
            exc_type, exc_value, exc_traceback = sys.exc_info()
            if exc_type:
                record.exception_type = exc_type.__name__
                record.exception_message = str(exc_value)
                record.exception_traceback = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 添加自定义字段
        for key, value in kwargs.items():
            if key not in ['exc_info']:
                record.extra_fields[key] = value
        
        return record
    
    def debug(self, message: str, **kwargs):
        """记录DEBUG日志
        
        Args:
            message: 日志消息
            **kwargs: 额外字段
        """
        self.log(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """记录INFO日志
        
        Args:
            message: 日志消息
            **kwargs: 额外字段
        """
        self.log(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录WARNING日志
        
        Args:
            message: 日志消息
            **kwargs: 额外字段
        """
        self.log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录ERROR日志
        
        Args:
            message: 日志消息
            **kwargs: 额外字段
        """
        self.log(LogLevel.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录CRITICAL日志
        
        Args:
            message: 日志消息
            **kwargs: 额外字段
        """
        self.log(LogLevel.CRITICAL, message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """记录异常日志
        
        Args:
            message: 日志消息
            **kwargs: 额外字段
        """
        kwargs['exc_info'] = True
        self.log(LogLevel.ERROR, message, **kwargs)
    
    def flush(self):
        """刷新所有输出"""
        for output in self.outputs:
            output.flush()
    
    def close(self):
        """关闭日志记录器"""
        for output in self.outputs:
            output.close()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        uptime = datetime.now() - self.stats['start_time']
        
        return {
            'name': self.name,
            'level': self.config.level.name,
            'output_type': self.config.output_type.value,
            'total_logs': self.stats['total_logs'],
            'logs_by_level': self.stats['logs_by_level'].copy(),
            'errors': self.stats['errors'],
            'uptime_seconds': uptime.total_seconds(),
            'logs_per_second': self.stats['total_logs'] / max(uptime.total_seconds(), 1)
        }


class LoggingManager:
    """日志管理器"""
    
    def __init__(self):
        self.loggers: Dict[str, Logger] = {}
        self.default_config = LogConfig(
            name='default',
            level=LogLevel.INFO,
            format=LogFormat.DETAILED,
            output_type=OutputType.CONSOLE
        )
        self._lock = threading.RLock()
    
    def get_logger(self, name: str, config: Optional[LogConfig] = None) -> Logger:
        """获取日志记录器
        
        Args:
            name: 记录器名称
            config: 日志配置
        
        Returns:
            Logger: 日志记录器
        """
        with self._lock:
            if name not in self.loggers:
                logger_config = config or self.default_config
                logger_config.name = name
                self.loggers[name] = Logger(name, logger_config)
            
            return self.loggers[name]
    
    def set_default_config(self, config: LogConfig):
        """设置默认配置
        
        Args:
            config: 日志配置
        """
        self.default_config = config
    
    def set_level(self, level: LogLevel, logger_name: Optional[str] = None):
        """设置日志级别
        
        Args:
            level: 日志级别
            logger_name: 记录器名称，None表示所有记录器
        """
        with self._lock:
            if logger_name:
                if logger_name in self.loggers:
                    self.loggers[logger_name].config.level = level
            else:
                # 设置所有记录器的级别
                for logger in self.loggers.values():
                    logger.config.level = level
                
                # 设置默认配置的级别
                self.default_config.level = level
    
    def flush_all(self):
        """刷新所有记录器"""
        with self._lock:
            for logger in self.loggers.values():
                logger.flush()
    
    def close_all(self):
        """关闭所有记录器"""
        with self._lock:
            for logger in self.loggers.values():
                logger.close()
            
            self.loggers.clear()
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = {
                'total_loggers': len(self.loggers),
                'loggers': {}
            }
            
            for name, logger in self.loggers.items():
                stats['loggers'][name] = logger.get_stats()
            
            return stats


# 全局日志管理器实例
_logging_manager = None


def get_logging_manager() -> LoggingManager:
    """获取日志管理器实例
    
    Returns:
        LoggingManager: 日志管理器实例
    """
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    return _logging_manager


def get_logger(name: str, config: Optional[LogConfig] = None) -> Logger:
    """获取日志记录器
    
    Args:
        name: 记录器名称
        config: 日志配置
    
    Returns:
        Logger: 日志记录器
    """
    return get_logging_manager().get_logger(name, config)


def set_log_level(level: LogLevel, logger_name: Optional[str] = None):
    """设置日志级别
    
    Args:
        level: 日志级别
        logger_name: 记录器名称
    """
    get_logging_manager().set_level(level, logger_name)


# 性能监控装饰器
def log_performance(func: Optional[Callable] = None, *, logger_name: Optional[str] = None, level: LogLevel = LogLevel.INFO):
    """性能监控装饰器
    
    Args:
        func: 被装饰的函数
        logger_name: 记录器名称
        level: 日志级别
    
    Returns:
        装饰器函数
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or f.__module__ or 'performance')
            
            start_time = time.time()
            start_memory = 0
            
            try:
                # 获取内存使用情况（如果可用）
                import psutil
                process = psutil.Process()
                start_memory = process.memory_info().rss
            except ImportError:
                pass
            
            try:
                result = f(*args, **kwargs)
                
                # 计算执行时间
                execution_time = time.time() - start_time
                
                # 计算内存使用变化
                memory_delta = 0
                try:
                    end_memory = process.memory_info().rss
                    memory_delta = end_memory - start_memory
                except (NameError, AttributeError):
                    pass
                
                # 记录性能日志
                logger.log(
                    level,
                    f"函数 {f.__name__} 执行完成",
                    execution_time=execution_time,
                    memory_delta=memory_delta,
                    function=f.__name__,
                    module=f.__module__
                )
                
                return result
            
            except Exception as e:
                execution_time = time.time() - start_time
                
                logger.error(
                    f"函数 {f.__name__} 执行失败: {e}",
                    execution_time=execution_time,
                    function=f.__name__,
                    module=f.__module__,
                    exc_info=True
                )
                
                raise
        
        return wrapper
    
    if func is None:
        return decorator
    else:
        return decorator(func)


# 导出的公共接口
__all__ = [
    'LogLevel',
    'LogFormat',
    'OutputType',
    'RotationPolicy',
    'LogConfig',
    'LogRecord',
    'LogFormatter',
    'LogOutput',
    'ConsoleOutput',
    'FileOutput',
    'MemoryOutput',
    'Logger',
    'LoggingManager',
    'get_logging_manager',
    'get_logger',
    'set_log_level',
    'log_performance'
]