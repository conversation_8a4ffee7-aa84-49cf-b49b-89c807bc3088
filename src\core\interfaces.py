"""
核心接口定义
定义系统中各个组件的标准接口，实现松耦合架构
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import pandas as pd


class PredictionType(Enum):
    """预测类型枚举"""
    ODD_EVEN_RATIO = "odd_even_ratio"
    SIZE_RATIO = "size_ratio"
    KILL_NUMBERS = "kill_numbers"
    NUMBER_GENERATION = "number_generation"


class BallType(Enum):
    """球类型枚举"""
    RED = "red"
    BLUE = "blue"


@dataclass
class PredictionResult:
    """预测结果数据类"""
    prediction_type: PredictionType
    ball_type: BallType
    value: Any
    confidence: float
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ValidationResult:
    """验证结果数据类"""
    is_valid: bool
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class IDataValidator(ABC):
    """数据验证器接口"""
    
    @abstractmethod
    def validate_lottery_data(self, data: pd.DataFrame) -> ValidationResult:
        """验证彩票数据"""
        pass
    
    @abstractmethod
    def validate_prediction_input(self, data: Dict[str, Any]) -> ValidationResult:
        """验证预测输入"""
        pass


class IPredictor(ABC):
    """预测器接口"""
    
    @abstractmethod
    def predict(self, data: pd.DataFrame, target_index: int, **kwargs) -> PredictionResult:
        """执行预测"""
        pass
    
    @abstractmethod
    def train(self, data: pd.DataFrame, **kwargs) -> bool:
        """训练模型"""
        pass
    
    @abstractmethod
    def get_prediction_type(self) -> PredictionType:
        """获取预测类型"""
        pass


class IAsyncPredictor(ABC):
    """异步预测器接口"""
    
    @abstractmethod
    async def predict_async(self, data: pd.DataFrame, target_index: int, **kwargs) -> PredictionResult:
        """异步预测"""
        pass
    
    @abstractmethod
    async def batch_predict_async(self, data: pd.DataFrame, target_indices: List[int], **kwargs) -> List[PredictionResult]:
        """批量异步预测"""
        pass


class INumberGenerator(ABC):
    """号码生成器接口"""
    
    @abstractmethod
    def generate_numbers(self, predictions: List[PredictionResult], **kwargs) -> Tuple[List[int], List[int]]:
        """生成号码"""
        pass
    
    @abstractmethod
    def apply_kill_numbers(self, candidates: List[int], kill_numbers: List[int]) -> List[int]:
        """应用杀号"""
        pass


class IKillNumberPredictor(ABC):
    """杀号预测器接口"""
    
    @abstractmethod
    def predict_kill_numbers(self, data: pd.DataFrame, target_index: int, ball_type: BallType, count: int) -> List[int]:
        """预测杀号"""
        pass
    
    @abstractmethod
    def get_success_rate(self, data: pd.DataFrame, periods: int) -> float:
        """获取成功率"""
        pass


class IEvaluator(ABC):
    """评估器接口"""
    
    @abstractmethod
    def evaluate_prediction(self, prediction: PredictionResult, actual: Any) -> Dict[str, float]:
        """评估预测结果"""
        pass
    
    @abstractmethod
    def calculate_hit_rate(self, predictions: List[PredictionResult], actuals: List[Any]) -> float:
        """计算命中率"""
        pass


class IConfigManager(ABC):
    """配置管理器接口"""
    
    @abstractmethod
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置"""
        pass
    
    @abstractmethod
    def set_config(self, key: str, value: Any) -> None:
        """设置配置"""
        pass
    
    @abstractmethod
    def load_config(self, config_path: str) -> bool:
        """加载配置"""
        pass


class ILogger(ABC):
    """日志记录器接口"""
    
    @abstractmethod
    def log_prediction(self, prediction: PredictionResult) -> None:
        """记录预测"""
        pass
    
    @abstractmethod
    def log_performance(self, operation: str, duration: float, metadata: Dict[str, Any] = None) -> None:
        """记录性能"""
        pass
    
    @abstractmethod
    def log_error(self, error: Exception, context: Dict[str, Any] = None) -> None:
        """记录错误"""
        pass


class IEventBus(ABC):
    """事件总线接口"""
    
    @abstractmethod
    def subscribe(self, event_type: str, handler: callable) -> None:
        """订阅事件"""
        pass
    
    @abstractmethod
    def unsubscribe(self, event_type: str, handler: callable) -> None:
        """取消订阅"""
        pass
    
    @abstractmethod
    def publish(self, event_type: str, data: Any) -> None:
        """发布事件"""
        pass


class IPredictionStrategy(ABC):
    """预测策略接口"""
    
    @abstractmethod
    def execute(self, data: pd.DataFrame, target_index: int, config: Dict[str, Any]) -> List[PredictionResult]:
        """执行预测策略"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass


class IDataProcessor(ABC):
    """数据处理器接口"""
    
    @abstractmethod
    def process_raw_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """处理原始数据"""
        pass
    
    @abstractmethod
    def extract_features(self, data: pd.DataFrame, target_index: int) -> Dict[str, Any]:
        """提取特征"""
        pass
    
    @abstractmethod
    def normalize_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """标准化数据"""
        pass


class ICacheManager(ABC):
    """缓存管理器接口"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存"""
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """清空缓存"""
        pass


class IMetricsCollector(ABC):
    """指标收集器接口"""
    
    @abstractmethod
    def record_prediction_time(self, duration: float) -> None:
        """记录预测时间"""
        pass
    
    @abstractmethod
    def record_hit_rate(self, rate: float) -> None:
        """记录命中率"""
        pass
    
    @abstractmethod
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        pass