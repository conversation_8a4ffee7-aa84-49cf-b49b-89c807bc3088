"""统一数据库管理系统

提供数据库连接、事务管理、ORM和查询构建功能。
"""

import time
import json
import sqlite3
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Type, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from contextlib import contextmanager
from abc import ABC, abstractmethod
import queue
import weakref
from pathlib import Path

from .exceptions import DataException, LotteryPredictorException, ErrorCode
from .logging_manager import get_logger, log_performance
from .cache_manager import get_cache_manager
from .performance_monitor import record_metric


class DatabaseType(Enum):
    """数据库类型"""
    SQLITE = 'sqlite'
    MYSQL = 'mysql'
    POSTGRESQL = 'postgresql'
    MONGODB = 'mongodb'
    REDIS = 'redis'
    MEMORY = 'memory'


class TransactionIsolation(Enum):
    """事务隔离级别"""
    READ_UNCOMMITTED = 'READ_UNCOMMITTED'
    READ_COMMITTED = 'READ_COMMITTED'
    REPEATABLE_READ = 'REPEATABLE_READ'
    SERIALIZABLE = 'SERIALIZABLE'


class QueryType(Enum):
    """查询类型"""
    SELECT = 'SELECT'
    INSERT = 'INSERT'
    UPDATE = 'UPDATE'
    DELETE = 'DELETE'
    CREATE = 'CREATE'
    DROP = 'DROP'
    ALTER = 'ALTER'


class FieldType(Enum):
    """字段类型"""
    INTEGER = 'INTEGER'
    FLOAT = 'FLOAT'
    STRING = 'STRING'
    TEXT = 'TEXT'
    BOOLEAN = 'BOOLEAN'
    DATE = 'DATE'
    DATETIME = 'DATETIME'
    TIMESTAMP = 'TIMESTAMP'
    JSON = 'JSON'
    BLOB = 'BLOB'


@dataclass
class DatabaseConfig:
    """数据库配置"""
    type: DatabaseType
    host: str = 'localhost'
    port: int = 0
    database: str = ''
    username: str = ''
    password: str = ''
    
    # 连接池配置
    min_connections: int = 1
    max_connections: int = 10
    connection_timeout: int = 30
    idle_timeout: int = 300
    
    # 其他配置
    charset: str = 'utf8mb4'
    autocommit: bool = False
    ssl_enabled: bool = False
    
    # 扩展配置
    options: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """设置默认端口"""
        if self.port == 0:
            port_map = {
                DatabaseType.MYSQL: 3306,
                DatabaseType.POSTGRESQL: 5432,
                DatabaseType.MONGODB: 27017,
                DatabaseType.REDIS: 6379,
                DatabaseType.SQLITE: 0,
                DatabaseType.MEMORY: 0
            }
            self.port = port_map.get(self.type, 0)


@dataclass
class QueryResult:
    """查询结果"""
    rows: List[Dict[str, Any]] = field(default_factory=list)
    affected_rows: int = 0
    last_insert_id: Optional[int] = None
    execution_time: float = 0.0
    query_type: Optional[QueryType] = None
    
    # 分页信息
    total_count: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None
    
    # 元数据
    columns: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Field:
    """数据库字段定义"""
    name: str
    type: FieldType
    nullable: bool = True
    default: Any = None
    primary_key: bool = False
    unique: bool = False
    auto_increment: bool = False
    foreign_key: Optional[str] = None
    index: bool = False
    length: Optional[int] = None
    precision: Optional[int] = None
    scale: Optional[int] = None
    comment: str = ""


@dataclass
class Table:
    """数据库表定义"""
    name: str
    fields: List[Field]
    indexes: List[Dict[str, Any]] = field(default_factory=list)
    constraints: List[Dict[str, Any]] = field(default_factory=list)
    comment: str = ""
    
    def get_field(self, name: str) -> Optional[Field]:
        """获取字段"""
        for field in self.fields:
            if field.name == name:
                return field
        return None
    
    def get_primary_keys(self) -> List[Field]:
        """获取主键字段"""
        return [field for field in self.fields if field.primary_key]


class DatabaseConnection(ABC):
    """数据库连接抽象基类"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.connection = None
        self.is_connected = False
        self.last_used = time.time()
        self.transaction_level = 0
        self._lock = threading.RLock()
    
    @abstractmethod
    def connect(self):
        """连接数据库"""
        pass
    
    @abstractmethod
    def disconnect(self):
        """断开连接"""
        pass
    
    @abstractmethod
    def execute(self, query: str, params: Optional[Tuple] = None) -> QueryResult:
        """执行查询"""
        pass
    
    @abstractmethod
    def begin_transaction(self, isolation: Optional[TransactionIsolation] = None):
        """开始事务"""
        pass
    
    @abstractmethod
    def commit_transaction(self):
        """提交事务"""
        pass
    
    @abstractmethod
    def rollback_transaction(self):
        """回滚事务"""
        pass
    
    def is_alive(self) -> bool:
        """检查连接是否存活"""
        return self.is_connected and (time.time() - self.last_used) < self.config.idle_timeout
    
    def update_last_used(self):
        """更新最后使用时间"""
        self.last_used = time.time()


class SQLiteConnection(DatabaseConnection):
    """SQLite连接"""
    
    def connect(self):
        """连接SQLite数据库"""
        try:
            self.connection = sqlite3.connect(
                self.config.database,
                check_same_thread=False,
                timeout=self.config.connection_timeout
            )
            self.connection.row_factory = sqlite3.Row
            self.is_connected = True
            self.update_last_used()
        except Exception as e:
            raise DataException(f"SQLite连接失败: {e}", ErrorCode.DATABASE_CONNECTION_ERROR)
    
    def disconnect(self):
        """断开SQLite连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.is_connected = False
    
    def execute(self, query: str, params: Optional[Tuple] = None) -> QueryResult:
        """执行SQLite查询"""
        if not self.is_connected:
            self.connect()
        
        start_time = time.time()
        
        try:
            with self._lock:
                cursor = self.connection.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # 获取查询类型
                query_type = self._get_query_type(query)
                
                result = QueryResult(
                    query_type=query_type,
                    execution_time=time.time() - start_time
                )
                
                if query_type == QueryType.SELECT:
                    # 查询结果
                    rows = cursor.fetchall()
                    result.rows = [dict(row) for row in rows]
                    result.columns = [desc[0] for desc in cursor.description] if cursor.description else []
                else:
                    # 修改操作
                    result.affected_rows = cursor.rowcount
                    if query_type == QueryType.INSERT:
                        result.last_insert_id = cursor.lastrowid
                
                self.update_last_used()
                return result
        
        except Exception as e:
            raise DataException(f"SQLite查询执行失败: {e}", ErrorCode.DATABASE_QUERY_ERROR)
    
    def begin_transaction(self, isolation: Optional[TransactionIsolation] = None):
        """开始SQLite事务"""
        with self._lock:
            if self.transaction_level == 0:
                self.connection.execute('BEGIN')
            self.transaction_level += 1
    
    def commit_transaction(self):
        """提交SQLite事务"""
        with self._lock:
            if self.transaction_level > 0:
                self.transaction_level -= 1
                if self.transaction_level == 0:
                    self.connection.commit()
    
    def rollback_transaction(self):
        """回滚SQLite事务"""
        with self._lock:
            if self.transaction_level > 0:
                self.transaction_level = 0
                self.connection.rollback()
    
    def _get_query_type(self, query: str) -> QueryType:
        """获取查询类型"""
        query_upper = query.strip().upper()
        
        if query_upper.startswith('SELECT'):
            return QueryType.SELECT
        elif query_upper.startswith('INSERT'):
            return QueryType.INSERT
        elif query_upper.startswith('UPDATE'):
            return QueryType.UPDATE
        elif query_upper.startswith('DELETE'):
            return QueryType.DELETE
        elif query_upper.startswith('CREATE'):
            return QueryType.CREATE
        elif query_upper.startswith('DROP'):
            return QueryType.DROP
        elif query_upper.startswith('ALTER'):
            return QueryType.ALTER
        else:
            return QueryType.SELECT  # 默认


class MemoryConnection(DatabaseConnection):
    """内存数据库连接"""
    
    def __init__(self, config: DatabaseConfig):
        super().__init__(config)
        self.tables: Dict[str, List[Dict[str, Any]]] = {}
        self.schemas: Dict[str, Table] = {}
        self.auto_increment_counters: Dict[str, int] = {}
    
    def connect(self):
        """连接内存数据库"""
        self.is_connected = True
        self.update_last_used()
    
    def disconnect(self):
        """断开内存数据库连接"""
        self.is_connected = False
    
    def execute(self, query: str, params: Optional[Tuple] = None) -> QueryResult:
        """执行内存数据库查询"""
        if not self.is_connected:
            self.connect()
        
        start_time = time.time()
        
        try:
            # 简单的SQL解析（仅支持基本操作）
            query_type = self._get_query_type(query)
            result = QueryResult(
                query_type=query_type,
                execution_time=time.time() - start_time
            )
            
            if query_type == QueryType.SELECT:
                result = self._execute_select(query, params)
            elif query_type == QueryType.INSERT:
                result = self._execute_insert(query, params)
            elif query_type == QueryType.UPDATE:
                result = self._execute_update(query, params)
            elif query_type == QueryType.DELETE:
                result = self._execute_delete(query, params)
            elif query_type == QueryType.CREATE:
                result = self._execute_create(query, params)
            
            result.execution_time = time.time() - start_time
            self.update_last_used()
            return result
        
        except Exception as e:
            raise DataException(f"内存数据库查询执行失败: {e}", ErrorCode.DATABASE_QUERY_ERROR)
    
    def _execute_select(self, query: str, params: Optional[Tuple]) -> QueryResult:
        """执行SELECT查询"""
        # 简化实现：假设查询格式为 "SELECT * FROM table_name"
        parts = query.strip().split()
        if len(parts) >= 4 and parts[0].upper() == 'SELECT' and parts[2].upper() == 'FROM':
            table_name = parts[3]
            
            if table_name in self.tables:
                rows = self.tables[table_name].copy()
                columns = list(rows[0].keys()) if rows else []
                
                return QueryResult(
                    rows=rows,
                    columns=columns,
                    query_type=QueryType.SELECT
                )
        
        return QueryResult(query_type=QueryType.SELECT)
    
    def _execute_insert(self, query: str, params: Optional[Tuple]) -> QueryResult:
        """执行INSERT查询"""
        # 简化实现
        return QueryResult(
            affected_rows=1,
            last_insert_id=1,
            query_type=QueryType.INSERT
        )
    
    def _execute_update(self, query: str, params: Optional[Tuple]) -> QueryResult:
        """执行UPDATE查询"""
        # 简化实现
        return QueryResult(
            affected_rows=1,
            query_type=QueryType.UPDATE
        )
    
    def _execute_delete(self, query: str, params: Optional[Tuple]) -> QueryResult:
        """执行DELETE查询"""
        # 简化实现
        return QueryResult(
            affected_rows=1,
            query_type=QueryType.DELETE
        )
    
    def _execute_create(self, query: str, params: Optional[Tuple]) -> QueryResult:
        """执行CREATE查询"""
        # 简化实现
        return QueryResult(
            affected_rows=0,
            query_type=QueryType.CREATE
        )
    
    def _get_query_type(self, query: str) -> QueryType:
        """获取查询类型"""
        query_upper = query.strip().upper()
        
        if query_upper.startswith('SELECT'):
            return QueryType.SELECT
        elif query_upper.startswith('INSERT'):
            return QueryType.INSERT
        elif query_upper.startswith('UPDATE'):
            return QueryType.UPDATE
        elif query_upper.startswith('DELETE'):
            return QueryType.DELETE
        elif query_upper.startswith('CREATE'):
            return QueryType.CREATE
        elif query_upper.startswith('DROP'):
            return QueryType.DROP
        elif query_upper.startswith('ALTER'):
            return QueryType.ALTER
        else:
            return QueryType.SELECT
    
    def begin_transaction(self, isolation: Optional[TransactionIsolation] = None):
        """开始内存数据库事务"""
        with self._lock:
            self.transaction_level += 1
    
    def commit_transaction(self):
        """提交内存数据库事务"""
        with self._lock:
            if self.transaction_level > 0:
                self.transaction_level -= 1
    
    def rollback_transaction(self):
        """回滚内存数据库事务"""
        with self._lock:
            if self.transaction_level > 0:
                self.transaction_level = 0


class ConnectionPool:
    """数据库连接池"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.logger = get_logger('ConnectionPool')
        
        # 连接池
        self.available_connections: queue.Queue = queue.Queue(maxsize=config.max_connections)
        self.used_connections: weakref.WeakSet = weakref.WeakSet()
        self.total_connections = 0
        
        # 锁
        self._lock = threading.RLock()
        
        # 初始化最小连接数
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.config.min_connections):
            connection = self._create_connection()
            self.available_connections.put(connection)
            self.total_connections += 1
    
    def _create_connection(self) -> DatabaseConnection:
        """创建数据库连接"""
        if self.config.type == DatabaseType.SQLITE:
            return SQLiteConnection(self.config)
        elif self.config.type == DatabaseType.MEMORY:
            return MemoryConnection(self.config)
        else:
            raise DataException(f"不支持的数据库类型: {self.config.type}", ErrorCode.DATABASE_CONNECTION_ERROR)
    
    def get_connection(self, timeout: Optional[float] = None) -> DatabaseConnection:
        """获取数据库连接
        
        Args:
            timeout: 超时时间
        
        Returns:
            DatabaseConnection: 数据库连接
        """
        timeout = timeout or self.config.connection_timeout
        
        try:
            # 尝试从池中获取可用连接
            connection = self.available_connections.get(timeout=timeout)
            
            # 检查连接是否存活
            if not connection.is_alive():
                connection.disconnect()
                connection = self._create_connection()
            
            connection.connect()
            self.used_connections.add(connection)
            
            return connection
        
        except queue.Empty:
            # 池中没有可用连接，尝试创建新连接
            with self._lock:
                if self.total_connections < self.config.max_connections:
                    connection = self._create_connection()
                    connection.connect()
                    self.total_connections += 1
                    self.used_connections.add(connection)
                    return connection
                else:
                    raise DataException("连接池已满", ErrorCode.DATABASE_CONNECTION_ERROR)
    
    def return_connection(self, connection: DatabaseConnection):
        """归还数据库连接
        
        Args:
            connection: 数据库连接
        """
        if connection in self.used_connections:
            self.used_connections.discard(connection)
            
            if connection.is_alive():
                try:
                    self.available_connections.put_nowait(connection)
                except queue.Full:
                    # 池已满，关闭连接
                    connection.disconnect()
                    with self._lock:
                        self.total_connections -= 1
            else:
                # 连接已失效，关闭并减少计数
                connection.disconnect()
                with self._lock:
                    self.total_connections -= 1
    
    @contextmanager
    def get_connection_context(self, timeout: Optional[float] = None):
        """获取连接上下文管理器
        
        Args:
            timeout: 超时时间
        
        Yields:
            DatabaseConnection: 数据库连接
        """
        connection = self.get_connection(timeout)
        try:
            yield connection
        finally:
            self.return_connection(connection)
    
    def close_all(self):
        """关闭所有连接"""
        # 关闭可用连接
        while not self.available_connections.empty():
            try:
                connection = self.available_connections.get_nowait()
                connection.disconnect()
            except queue.Empty:
                break
        
        # 关闭使用中的连接
        for connection in list(self.used_connections):
            connection.disconnect()
        
        with self._lock:
            self.total_connections = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return {
                'total_connections': self.total_connections,
                'available_connections': self.available_connections.qsize(),
                'used_connections': len(self.used_connections),
                'max_connections': self.config.max_connections,
                'min_connections': self.config.min_connections
            }


class QueryBuilder:
    """SQL查询构建器"""
    
    def __init__(self, table_name: str):
        self.table_name = table_name
        self.reset()
    
    def reset(self):
        """重置查询构建器"""
        self._select_fields = []
        self._where_conditions = []
        self._join_clauses = []
        self._order_by = []
        self._group_by = []
        self._having_conditions = []
        self._limit_value = None
        self._offset_value = None
        self._update_data = {}
        self._insert_data = {}
        return self
    
    def select(self, *fields: str):
        """设置SELECT字段
        
        Args:
            *fields: 字段名
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._select_fields.extend(fields)
        return self
    
    def where(self, condition: str, *params):
        """添加WHERE条件
        
        Args:
            condition: 条件表达式
            *params: 参数
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._where_conditions.append((condition, params))
        return self
    
    def join(self, table: str, on_condition: str, join_type: str = 'INNER'):
        """添加JOIN子句
        
        Args:
            table: 表名
            on_condition: 连接条件
            join_type: 连接类型
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._join_clauses.append(f"{join_type} JOIN {table} ON {on_condition}")
        return self
    
    def order_by(self, field: str, direction: str = 'ASC'):
        """添加ORDER BY子句
        
        Args:
            field: 字段名
            direction: 排序方向
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._order_by.append(f"{field} {direction}")
        return self
    
    def group_by(self, *fields: str):
        """添加GROUP BY子句
        
        Args:
            *fields: 字段名
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._group_by.extend(fields)
        return self
    
    def having(self, condition: str, *params):
        """添加HAVING条件
        
        Args:
            condition: 条件表达式
            *params: 参数
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._having_conditions.append((condition, params))
        return self
    
    def limit(self, count: int, offset: int = 0):
        """设置LIMIT和OFFSET
        
        Args:
            count: 限制数量
            offset: 偏移量
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._limit_value = count
        self._offset_value = offset
        return self
    
    def set(self, **data):
        """设置UPDATE数据
        
        Args:
            **data: 更新数据
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._update_data.update(data)
        return self
    
    def values(self, **data):
        """设置INSERT数据
        
        Args:
            **data: 插入数据
        
        Returns:
            QueryBuilder: 查询构建器
        """
        self._insert_data.update(data)
        return self
    
    def build_select(self) -> Tuple[str, Tuple]:
        """构建SELECT查询
        
        Returns:
            Tuple[str, Tuple]: (SQL语句, 参数)
        """
        # SELECT子句
        if self._select_fields:
            select_clause = f"SELECT {', '.join(self._select_fields)}"
        else:
            select_clause = "SELECT *"
        
        # FROM子句
        from_clause = f"FROM {self.table_name}"
        
        # JOIN子句
        join_clause = ' '.join(self._join_clauses) if self._join_clauses else ''
        
        # WHERE子句
        where_clause = ''
        params = []
        if self._where_conditions:
            conditions = []
            for condition, condition_params in self._where_conditions:
                conditions.append(condition)
                params.extend(condition_params)
            where_clause = f"WHERE {' AND '.join(conditions)}"
        
        # GROUP BY子句
        group_by_clause = f"GROUP BY {', '.join(self._group_by)}" if self._group_by else ''
        
        # HAVING子句
        having_clause = ''
        if self._having_conditions:
            conditions = []
            for condition, condition_params in self._having_conditions:
                conditions.append(condition)
                params.extend(condition_params)
            having_clause = f"HAVING {' AND '.join(conditions)}"
        
        # ORDER BY子句
        order_by_clause = f"ORDER BY {', '.join(self._order_by)}" if self._order_by else ''
        
        # LIMIT子句
        limit_clause = ''
        if self._limit_value is not None:
            if self._offset_value:
                limit_clause = f"LIMIT {self._offset_value}, {self._limit_value}"
            else:
                limit_clause = f"LIMIT {self._limit_value}"
        
        # 组合SQL
        sql_parts = [select_clause, from_clause]
        if join_clause:
            sql_parts.append(join_clause)
        if where_clause:
            sql_parts.append(where_clause)
        if group_by_clause:
            sql_parts.append(group_by_clause)
        if having_clause:
            sql_parts.append(having_clause)
        if order_by_clause:
            sql_parts.append(order_by_clause)
        if limit_clause:
            sql_parts.append(limit_clause)
        
        sql = ' '.join(sql_parts)
        return sql, tuple(params)
    
    def build_insert(self) -> Tuple[str, Tuple]:
        """构建INSERT查询
        
        Returns:
            Tuple[str, Tuple]: (SQL语句, 参数)
        """
        if not self._insert_data:
            raise ValueError("没有插入数据")
        
        fields = list(self._insert_data.keys())
        placeholders = ', '.join(['?' for _ in fields])
        values = tuple(self._insert_data.values())
        
        sql = f"INSERT INTO {self.table_name} ({', '.join(fields)}) VALUES ({placeholders})"
        return sql, values
    
    def build_update(self) -> Tuple[str, Tuple]:
        """构建UPDATE查询
        
        Returns:
            Tuple[str, Tuple]: (SQL语句, 参数)
        """
        if not self._update_data:
            raise ValueError("没有更新数据")
        
        # SET子句
        set_clauses = []
        params = []
        for field, value in self._update_data.items():
            set_clauses.append(f"{field} = ?")
            params.append(value)
        
        set_clause = f"SET {', '.join(set_clauses)}"
        
        # WHERE子句
        where_clause = ''
        if self._where_conditions:
            conditions = []
            for condition, condition_params in self._where_conditions:
                conditions.append(condition)
                params.extend(condition_params)
            where_clause = f"WHERE {' AND '.join(conditions)}"
        
        # 组合SQL
        sql_parts = [f"UPDATE {self.table_name}", set_clause]
        if where_clause:
            sql_parts.append(where_clause)
        
        sql = ' '.join(sql_parts)
        return sql, tuple(params)
    
    def build_delete(self) -> Tuple[str, Tuple]:
        """构建DELETE查询
        
        Returns:
            Tuple[str, Tuple]: (SQL语句, 参数)
        """
        # WHERE子句
        where_clause = ''
        params = []
        if self._where_conditions:
            conditions = []
            for condition, condition_params in self._where_conditions:
                conditions.append(condition)
                params.extend(condition_params)
            where_clause = f"WHERE {' AND '.join(conditions)}"
        
        # 组合SQL
        sql_parts = [f"DELETE FROM {self.table_name}"]
        if where_clause:
            sql_parts.append(where_clause)
        
        sql = ' '.join(sql_parts)
        return sql, tuple(params)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.logger = get_logger('DatabaseManager')
        self.cache_manager = get_cache_manager()
        
        # 连接池管理
        self.connection_pools: Dict[str, ConnectionPool] = {}
        self.default_pool_name = 'default'
        
        # 表结构缓存
        self.table_schemas: Dict[str, Table] = {}
        
        # 锁
        self._lock = threading.RLock()
    
    def add_database(self, name: str, config: DatabaseConfig, set_as_default: bool = False):
        """添加数据库配置
        
        Args:
            name: 数据库名称
            config: 数据库配置
            set_as_default: 是否设为默认数据库
        """
        with self._lock:
            pool = ConnectionPool(config)
            self.connection_pools[name] = pool
            
            if set_as_default or name == 'default':
                self.default_pool_name = name
            
            self.logger.info(f"添加数据库配置: {name} ({config.type.value})")
    
    def get_connection(self, database: Optional[str] = None, timeout: Optional[float] = None) -> DatabaseConnection:
        """获取数据库连接
        
        Args:
            database: 数据库名称
            timeout: 超时时间
        
        Returns:
            DatabaseConnection: 数据库连接
        """
        db_name = database or self.default_pool_name
        
        if db_name not in self.connection_pools:
            raise DataException(f"数据库配置不存在: {db_name}", ErrorCode.DATABASE_CONNECTION_ERROR)
        
        return self.connection_pools[db_name].get_connection(timeout)
    
    @contextmanager
    def get_connection_context(self, database: Optional[str] = None, timeout: Optional[float] = None):
        """获取连接上下文管理器
        
        Args:
            database: 数据库名称
            timeout: 超时时间
        
        Yields:
            DatabaseConnection: 数据库连接
        """
        db_name = database or self.default_pool_name
        
        if db_name not in self.connection_pools:
            raise DataException(f"数据库配置不存在: {db_name}", ErrorCode.DATABASE_CONNECTION_ERROR)
        
        with self.connection_pools[db_name].get_connection_context(timeout) as connection:
            yield connection
    
    @contextmanager
    def transaction(self, database: Optional[str] = None, isolation: Optional[TransactionIsolation] = None):
        """事务上下文管理器
        
        Args:
            database: 数据库名称
            isolation: 事务隔离级别
        
        Yields:
            DatabaseConnection: 数据库连接
        """
        with self.get_connection_context(database) as connection:
            try:
                connection.begin_transaction(isolation)
                yield connection
                connection.commit_transaction()
            except Exception:
                connection.rollback_transaction()
                raise
    
    @log_performance
    def execute(self, query: str, params: Optional[Tuple] = None, database: Optional[str] = None, cache_key: Optional[str] = None, cache_ttl: int = 300) -> QueryResult:
        """执行查询
        
        Args:
            query: SQL查询
            params: 查询参数
            database: 数据库名称
            cache_key: 缓存键
            cache_ttl: 缓存TTL
        
        Returns:
            QueryResult: 查询结果
        """
        # 检查缓存
        if cache_key:
            cached_result = self.cache_manager.get(cache_key)
            if cached_result:
                self.logger.debug(f"从缓存获取查询结果: {cache_key}")
                return cached_result
        
        # 执行查询
        with self.get_connection_context(database) as connection:
            result = connection.execute(query, params)
            
            # 记录性能指标
            record_metric('database_query_duration', result.execution_time, {
                'database': database or self.default_pool_name,
                'query_type': result.query_type.value if result.query_type else 'unknown'
            })
            
            # 缓存结果（仅缓存SELECT查询）
            if cache_key and result.query_type == QueryType.SELECT:
                self.cache_manager.set(cache_key, result, ttl=cache_ttl)
            
            return result
    
    def query_builder(self, table_name: str) -> QueryBuilder:
        """创建查询构建器
        
        Args:
            table_name: 表名
        
        Returns:
            QueryBuilder: 查询构建器
        """
        return QueryBuilder(table_name)
    
    def create_table(self, table: Table, database: Optional[str] = None, if_not_exists: bool = True):
        """创建表
        
        Args:
            table: 表定义
            database: 数据库名称
            if_not_exists: 如果表不存在才创建
        """
        sql = self._generate_create_table_sql(table, if_not_exists)
        self.execute(sql, database=database)
        
        # 缓存表结构
        with self._lock:
            self.table_schemas[table.name] = table
        
        self.logger.info(f"创建表: {table.name}")
    
    def _generate_create_table_sql(self, table: Table, if_not_exists: bool) -> str:
        """生成创建表的SQL
        
        Args:
            table: 表定义
            if_not_exists: 如果表不存在才创建
        
        Returns:
            str: SQL语句
        """
        # 字段定义
        field_definitions = []
        for field in table.fields:
            field_def = f"{field.name} {self._get_field_type_sql(field)}"
            
            if field.primary_key:
                field_def += " PRIMARY KEY"
            
            if field.auto_increment:
                field_def += " AUTOINCREMENT"
            
            if not field.nullable:
                field_def += " NOT NULL"
            
            if field.unique:
                field_def += " UNIQUE"
            
            if field.default is not None:
                if isinstance(field.default, str):
                    field_def += f" DEFAULT '{field.default}'"
                else:
                    field_def += f" DEFAULT {field.default}"
            
            field_definitions.append(field_def)
        
        # 组合SQL
        if_not_exists_clause = "IF NOT EXISTS " if if_not_exists else ""
        sql = f"CREATE TABLE {if_not_exists_clause}{table.name} (\n  {',\n  '.join(field_definitions)}\n)"
        
        return sql
    
    def _get_field_type_sql(self, field: Field) -> str:
        """获取字段类型SQL
        
        Args:
            field: 字段定义
        
        Returns:
            str: 字段类型SQL
        """
        type_map = {
            FieldType.INTEGER: 'INTEGER',
            FieldType.FLOAT: 'REAL',
            FieldType.STRING: f'VARCHAR({field.length or 255})',
            FieldType.TEXT: 'TEXT',
            FieldType.BOOLEAN: 'BOOLEAN',
            FieldType.DATE: 'DATE',
            FieldType.DATETIME: 'DATETIME',
            FieldType.TIMESTAMP: 'TIMESTAMP',
            FieldType.JSON: 'TEXT',
            FieldType.BLOB: 'BLOB'
        }
        
        return type_map.get(field.type, 'TEXT')
    
    def get_table_schema(self, table_name: str) -> Optional[Table]:
        """获取表结构
        
        Args:
            table_name: 表名
        
        Returns:
            Optional[Table]: 表结构
        """
        with self._lock:
            return self.table_schemas.get(table_name)
    
    def close_all_connections(self):
        """关闭所有连接"""
        with self._lock:
            for pool in self.connection_pools.values():
                pool.close_all()
            
            self.logger.info("所有数据库连接已关闭")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = {
                'databases': len(self.connection_pools),
                'default_database': self.default_pool_name,
                'table_schemas': len(self.table_schemas),
                'connection_pools': {}
            }
            
            for name, pool in self.connection_pools.items():
                stats['connection_pools'][name] = pool.get_stats()
            
            return stats
    
    def export_config(self, file_path: str):
        """导出配置
        
        Args:
            file_path: 文件路径
        """
        try:
            config_data = {
                'databases': {},
                'table_schemas': {},
                'statistics': self.get_statistics()
            }
            
            with self._lock:
                # 导出数据库配置
                for name, pool in self.connection_pools.items():
                    config_data['databases'][name] = {
                        'type': pool.config.type.value,
                        'host': pool.config.host,
                        'port': pool.config.port,
                        'database': pool.config.database,
                        'min_connections': pool.config.min_connections,
                        'max_connections': pool.config.max_connections,
                        'connection_timeout': pool.config.connection_timeout,
                        'idle_timeout': pool.config.idle_timeout
                    }
                
                # 导出表结构
                for name, table in self.table_schemas.items():
                    config_data['table_schemas'][name] = {
                        'name': table.name,
                        'fields': [
                            {
                                'name': field.name,
                                'type': field.type.value,
                                'nullable': field.nullable,
                                'primary_key': field.primary_key,
                                'unique': field.unique,
                                'auto_increment': field.auto_increment,
                                'length': field.length,
                                'comment': field.comment
                            }
                            for field in table.fields
                        ],
                        'comment': table.comment
                    }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"数据库配置已导出到: {file_path}")
        
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            raise LotteryPredictorException(f"导出配置失败: {e}", ErrorCode.FILE_WRITE_ERROR)


# 全局数据库管理器实例
_database_manager = None


def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例
    
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
    return _database_manager


# 便捷函数
def execute_query(query: str, params: Optional[Tuple] = None, database: Optional[str] = None, cache_key: Optional[str] = None, cache_ttl: int = 300) -> QueryResult:
    """执行查询
    
    Args:
        query: SQL查询
        params: 查询参数
        database: 数据库名称
        cache_key: 缓存键
        cache_ttl: 缓存TTL
    
    Returns:
        QueryResult: 查询结果
    """
    return get_database_manager().execute(query, params, database, cache_key, cache_ttl)


def query_builder(table_name: str) -> QueryBuilder:
    """创建查询构建器
    
    Args:
        table_name: 表名
    
    Returns:
        QueryBuilder: 查询构建器
    """
    return get_database_manager().query_builder(table_name)


@contextmanager
def transaction(database: Optional[str] = None, isolation: Optional[TransactionIsolation] = None):
    """事务上下文管理器
    
    Args:
        database: 数据库名称
        isolation: 事务隔离级别
    
    Yields:
        DatabaseConnection: 数据库连接
    """
    with get_database_manager().transaction(database, isolation) as connection:
        yield connection


# 导出的公共接口
__all__ = [
    'DatabaseType',
    'TransactionIsolation',
    'QueryType',
    'FieldType',
    'DatabaseConfig',
    'QueryResult',
    'Field',
    'Table',
    'DatabaseConnection',
    'SQLiteConnection',
    'MemoryConnection',
    'ConnectionPool',
    'QueryBuilder',
    'DatabaseManager',
    'get_database_manager',
    'execute_query',
    'query_builder',
    'transaction'
]