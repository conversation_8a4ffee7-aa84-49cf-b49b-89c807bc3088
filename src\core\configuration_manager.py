"""
配置管理器
统一管理系统配置，支持多种配置源和热更新
"""

from typing import Dict, Any, Optional, List, Union
import json
import yaml
from pathlib import Path
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
import os
from datetime import datetime

from .interfaces import LotteryConfig


@dataclass
class PredictorConfig:
    """预测器配置"""
    name: str
    module: str
    class_name: str
    type: str
    enabled: bool = True
    singleton: bool = True
    weight: float = 1.0
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


@dataclass
class SystemConfig:
    """系统配置"""
    lottery_config: LotteryConfig
    predictors: List[PredictorConfig]
    logging: Dict[str, Any]
    performance: Dict[str, Any]
    data_sources: Dict[str, str]
    
    def __post_init__(self):
        # 确保默认值
        if not hasattr(self, 'logging') or self.logging is None:
            self.logging = {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': 'logs/system.log'
            }
        
        if not hasattr(self, 'performance') or self.performance is None:
            self.performance = {
                'enable_cache': True,
                'cache_size': 1000,
                'parallel_processing': True,
                'max_workers': 4
            }
        
        if not hasattr(self, 'data_sources') or self.data_sources is None:
            self.data_sources = {
                'default': 'data/raw/dlt_data.csv'
            }


class ConfigurationSource(ABC):
    """配置源接口"""
    
    @abstractmethod
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        pass
    
    @abstractmethod
    def save_config(self, config: Dict[str, Any]) -> None:
        """保存配置"""
        pass


class JsonConfigurationSource(ConfigurationSource):
    """JSON配置源"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
    
    def load_config(self) -> Dict[str, Any]:
        """加载JSON配置"""
        if not self.file_path.exists():
            return {}
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            raise ValueError(f"加载JSON配置失败: {e}")
    
    def save_config(self, config: Dict[str, Any]) -> None:
        """保存JSON配置"""
        try:
            # 确保目录存在
            self.file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise ValueError(f"保存JSON配置失败: {e}")


class YamlConfigurationSource(ConfigurationSource):
    """YAML配置源"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
    
    def load_config(self) -> Dict[str, Any]:
        """加载YAML配置"""
        if not self.file_path.exists():
            return {}
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            raise ValueError(f"加载YAML配置失败: {e}")
    
    def save_config(self, config: Dict[str, Any]) -> None:
        """保存YAML配置"""
        try:
            # 确保目录存在
            self.file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            raise ValueError(f"保存YAML配置失败: {e}")


class EnvironmentConfigurationSource(ConfigurationSource):
    """环境变量配置源"""
    
    def __init__(self, prefix: str = 'LOTTERY_'):
        self.prefix = prefix
    
    def load_config(self) -> Dict[str, Any]:
        """从环境变量加载配置"""
        config = {}
        
        for key, value in os.environ.items():
            if key.startswith(self.prefix):
                config_key = key[len(self.prefix):].lower()
                
                # 尝试转换数据类型
                try:
                    # 尝试解析为JSON
                    config[config_key] = json.loads(value)
                except json.JSONDecodeError:
                    # 尝试转换为数字
                    try:
                        if '.' in value:
                            config[config_key] = float(value)
                        else:
                            config[config_key] = int(value)
                    except ValueError:
                        # 保持字符串
                        config[config_key] = value
        
        return config
    
    def save_config(self, config: Dict[str, Any]) -> None:
        """环境变量配置源不支持保存"""
        raise NotImplementedError("环境变量配置源不支持保存操作")


class ConfigurationManager:
    """配置管理器"""
    
    def __init__(self):
        self._sources: List[ConfigurationSource] = []
        self._config: Optional[SystemConfig] = None
        self._watchers: List[callable] = []
    
    def add_source(self, source: ConfigurationSource) -> None:
        """添加配置源"""
        self._sources.append(source)
    
    def add_json_source(self, file_path: str) -> None:
        """添加JSON配置源"""
        self.add_source(JsonConfigurationSource(file_path))
    
    def add_yaml_source(self, file_path: str) -> None:
        """添加YAML配置源"""
        self.add_source(YamlConfigurationSource(file_path))
    
    def add_environment_source(self, prefix: str = 'LOTTERY_') -> None:
        """添加环境变量配置源"""
        self.add_source(EnvironmentConfigurationSource(prefix))
    
    def load_config(self) -> SystemConfig:
        """加载配置"""
        merged_config = {}
        
        # 按顺序合并所有配置源
        for source in self._sources:
            try:
                source_config = source.load_config()
                merged_config = self._merge_config(merged_config, source_config)
            except Exception as e:
                print(f"⚠️ 配置源加载失败: {e}")
        
        # 如果没有配置，使用默认配置
        if not merged_config:
            merged_config = self._get_default_config()
        
        # 转换为SystemConfig对象
        self._config = self._parse_system_config(merged_config)
        
        # 通知观察者
        self._notify_watchers()
        
        return self._config
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'lottery_config': {
                'red_ball_range': [1, 35],
                'blue_ball_range': [1, 12],
                'red_ball_count': 5,
                'blue_ball_count': 2
            },
            'predictors': [
                {
                    'name': 'enhanced_kill_system',
                    'module': 'src.algorithms.enhanced_kill_system',
                    'class_name': 'EnhancedKillSystem',
                    'type': 'kill_number',
                    'enabled': True,
                    'singleton': True,
                    'weight': 1.0,
                    'parameters': {}
                },
                {
                    'name': 'enhanced_bayesian',
                    'module': 'src.algorithms.enhanced_bayesian',
                    'class_name': 'EnhancedBayesianPredictor',
                    'type': 'probability',
                    'enabled': True,
                    'singleton': True,
                    'weight': 1.0,
                    'parameters': {}
                }
            ],
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': 'logs/system.log'
            },
            'performance': {
                'enable_cache': True,
                'cache_size': 1000,
                'parallel_processing': True,
                'max_workers': 4
            },
            'data_sources': {
                'default': 'data/raw/dlt_data.csv'
            }
        }
    
    def _parse_system_config(self, config_dict: Dict[str, Any]) -> SystemConfig:
        """解析系统配置"""
        # 解析彩票配置
        lottery_cfg = config_dict.get('lottery_config', {})
        lottery_config = LotteryConfig(
            red_ball_range=tuple(lottery_cfg.get('red_ball_range', (1, 35))),
            blue_ball_range=tuple(lottery_cfg.get('blue_ball_range', (1, 12))),
            red_ball_count=lottery_cfg.get('red_ball_count', 5),
            blue_ball_count=lottery_cfg.get('blue_ball_count', 2)
        )
        
        # 解析预测器配置
        predictors = []
        for pred_cfg in config_dict.get('predictors', []):
            predictor = PredictorConfig(
                name=pred_cfg['name'],
                module=pred_cfg['module'],
                class_name=pred_cfg['class_name'],
                type=pred_cfg['type'],
                enabled=pred_cfg.get('enabled', True),
                singleton=pred_cfg.get('singleton', True),
                weight=pred_cfg.get('weight', 1.0),
                parameters=pred_cfg.get('parameters', {})
            )
            predictors.append(predictor)
        
        return SystemConfig(
            lottery_config=lottery_config,
            predictors=predictors,
            logging=config_dict.get('logging', {}),
            performance=config_dict.get('performance', {}),
            data_sources=config_dict.get('data_sources', {})
        )
    
    def save_config(self, config: SystemConfig = None) -> None:
        """保存配置"""
        if config is None:
            config = self._config
        
        if config is None:
            raise ValueError("没有可保存的配置")
        
        # 转换为字典
        config_dict = self._serialize_config(config)
        
        # 保存到第一个支持保存的配置源
        for source in self._sources:
            try:
                if not isinstance(source, EnvironmentConfigurationSource):
                    source.save_config(config_dict)
                    print(f"✅ 配置已保存到: {source}")
                    return
            except Exception as e:
                print(f"⚠️ 配置保存失败: {e}")
        
        raise ValueError("没有可用的配置保存源")
    
    def _serialize_config(self, config: SystemConfig) -> Dict[str, Any]:
        """序列化配置"""
        return {
            'lottery_config': {
                'red_ball_range': list(config.lottery_config.red_ball_range),
                'blue_ball_range': list(config.lottery_config.blue_ball_range),
                'red_ball_count': config.lottery_config.red_ball_count,
                'blue_ball_count': config.lottery_config.blue_ball_count
            },
            'predictors': [asdict(pred) for pred in config.predictors],
            'logging': config.logging,
            'performance': config.performance,
            'data_sources': config.data_sources
        }
    
    def get_config(self) -> SystemConfig:
        """获取当前配置"""
        if self._config is None:
            return self.load_config()
        return self._config
    
    def get_predictor_config(self, name: str) -> Optional[PredictorConfig]:
        """获取预测器配置"""
        if self._config is None:
            self.load_config()
        
        for predictor in self._config.predictors:
            if predictor.name == name:
                return predictor
        
        return None
    
    def get_enabled_predictors(self) -> List[PredictorConfig]:
        """获取启用的预测器配置"""
        if self._config is None:
            self.load_config()
        
        return [pred for pred in self._config.predictors if pred.enabled]
    
    def add_config_watcher(self, callback: callable) -> None:
        """添加配置变更观察者"""
        self._watchers.append(callback)
    
    def _notify_watchers(self) -> None:
        """通知配置变更观察者"""
        for watcher in self._watchers:
            try:
                watcher(self._config)
            except Exception as e:
                print(f"⚠️ 配置观察者通知失败: {e}")
    
    def reload_config(self) -> SystemConfig:
        """重新加载配置"""
        return self.load_config()


# 全局配置管理器
_config_manager = None


def get_config_manager() -> ConfigurationManager:
    """获取全局配置管理器"""
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigurationManager()
        
        # 添加默认配置源
        _config_manager.add_json_source('config/system.json')
        _config_manager.add_yaml_source('config/system.yaml')
        _config_manager.add_environment_source()
    
    return _config_manager


def get_system_config() -> SystemConfig:
    """获取系统配置"""
    return get_config_manager().get_config()