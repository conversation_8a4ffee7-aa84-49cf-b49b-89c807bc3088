"""核心功能模块
提供基础类和接口定义
"""

# 导入基础类和接口
from .base import (
    BallType, FeatureType, PredictionResult, GenerationResult,
    AnalysisResult, BaseAnalyzer, BasePredictor, BaseGenerator, BaseSystem,
    StandardBasePredictor
)

# 导入项目配置
from .project_config import (
    project_config,
    get_project_root,
    get_src_path,
    get_data_path,
    get_config_path,
    get_logs_path,
    get_models_path
)
from .base import StandardBasePredictor

# 日志管理
from .logging_manager import (
    LogLevel,
    LogFormat,
    OutputType,
    RotationPolicy,
    LogConfig,
    LogRecord,
    LogFormatter,
    LogOutput,
    ConsoleOutput,
    FileOutput,
    MemoryOutput,
    Logger,
    LoggingManager,
    get_logging_manager,
    get_logger,
    set_log_level,
    log_performance
)
from .exceptions import (
    ErrorCode,
    LotteryPredictorException,
    DataException,
    ModelException,
    PredictionException,
    ConfigurationException,
    ValidationException,
    AlgorithmException,
    ResourceException
)

# 导出配置管理相关组件
from .config_manager import (
    ConfigFormat,
    ConfigSource
)

# 导出数据处理相关组件
from .data_processor import (
    DataType,
    ProcessingStage,
    DataSchema,
    ProcessingStep,
    DataQualityMetrics,
    BaseDataProcessor,
    LotteryDataCleaner,
    LotteryDataValidator,
    LotteryDataTransformer,
    UnifiedDataProcessor,
    get_data_processor
)

# 缓存管理
from .cache_manager import (
    CacheBackend,
    EvictionPolicy,
    SerializationType,
    CacheOperation,
    CacheConfig,
    CacheStats,
    CacheEntry,
    CacheSerializer,
    JSONSerializer,
    PickleSerializer,
    StringSerializer,
    CacheBackendInterface,
    MemoryCacheBackend,
    FileCacheBackend,
    CacheManager,
    get_cache_manager,
    cache_result,
    cache_clear,
    cache_stats,
    cached_property
)

# 事件管理
from .event_manager import (
    EventType, EventPriority, EventStatus, HandlerType,
    EventConfig, Event, EventStats,
    EventFilter, TypeEventFilter, PriorityEventFilter, NameEventFilter,
    EventHandler, EventPersistence, FilePersistence,
    EventManager,
    get_event_manager, event_handler, global_event_handler,
    publish_event, emit_event, get_event_stats, wait_for_event
)

# 导出性能监控相关组件
from .performance_monitor import (
    MetricType,
    AlertLevel,
    MetricValue,
    PerformanceMetrics,
    AlertRule,
    Alert,
    MetricCollector,
    SystemMonitor,
    PerformanceMonitor,
    monitor_performance,
    time_it,
    get_performance_monitor,
    record_metric,
    get_health_status
)

# 导出任务调度相关组件
from .task_scheduler import (
    TaskStatus,
    TaskPriority,
    ScheduleType,
    TaskResult,
    Task,
    TaskQueue,
    TaskExecutor,
    CronParser,
    TaskScheduler,
    scheduled_task,
    async_task,
    get_task_scheduler,
    add_task,
    get_task_status
)

# 导入API网关相关组件
from .api_gateway import (
    HTTPMethod,
    AuthType,
    RateLimitType,
    ResponseFormat,
    APIRequest,
    APIResponse,
    RateLimitRule,
    APIEndpoint,
    RateLimiter,
    AuthManager,
    RequestLogger,
    APIGateway,
    api_endpoint,
    require_auth,
    rate_limit,
    get_api_gateway
)

# 导入数据库管理相关组件
from .database_manager import (
    DatabaseType,
    TransactionIsolation,
    QueryType,
    FieldType,
    DatabaseConfig,
    QueryResult,
    Field,
    Table,
    DatabaseConnection,
    SQLiteConnection,
    MemoryConnection,
    ConnectionPool,
    QueryBuilder,
    DatabaseManager,
    get_database_manager,
    execute_query,
    query_builder,
    transaction
)
from .config_manager import (
    ConfigFormat,
    ConfigSource,
    ValidationLevel,
    ConfigSchema,
    ConfigMetadata,
    ConfigChangeEvent,
    ConfigLoader,
    ConfigValidator,
    ConfigWatcher,
    ConfigManager,
    get_config_manager,
    load_config,
    get_config,
    get_config_value,
    set_config_value,
    config_required,
)

__all__ = [
    # 基础模块
    'StandardBasePredictor',
    'LoggerMixin',
    
    # 日志管理
    'LogLevel',
    'LogFormat',
    'OutputType',
    'RotationPolicy',
    'LogConfig',
    'LogRecord',
    'LogFormatter',
    'LogOutput',
    'ConsoleOutput',
    'FileOutput',
    'MemoryOutput',
    'Logger',
    'LoggingManager',
    'get_logging_manager',
    'get_logger',
    'set_log_level',
    'log_performance',
    
    # 异常处理
    'ErrorCode',
    'LotteryPredictorException',
    'DataException',
    'ModelException',
    'PredictionException',
    'FileSystemException',
    'NetworkException',
    'AlgorithmException',
    'SystemException',
    'handle_exception',
    'exception_handler',
    'safe_execute',
    
    # 配置管理
    'ConfigFormat',
    'Environment',
    'ConfigSource',
    'ConfigValidationRule',
    'UnifiedConfigManager',
    'get_config_manager',
    'get_config',
    'set_config',
    
    # 数据处理
    'DataType',
    'ProcessingStage',
    'DataSchema',
    'ProcessingStep',
    'DataQualityMetrics',
    'BaseDataProcessor',
    'LotteryDataCleaner',
    'LotteryDataValidator',
    'LotteryDataTransformer',
    'UnifiedDataProcessor',
    'get_data_processor',
    
    # 缓存管理
    'CacheBackend',
    'CacheStrategy',
    'CacheEntry',
    'CacheStats',
    'BaseCacheBackend',
    'MemoryCacheBackend',
    'FileCacheBackend',
    'CacheEvictionStrategy',
    'LRUEvictionStrategy',
    'LFUEvictionStrategy',
    'TTLEvictionStrategy',
    'UnifiedCacheManager',
    'get_cache_manager',
    'cache_result',
    'memoize',
    
    # 性能监控
    'MetricType',
    'AlertLevel',
    'MetricValue',
    'PerformanceMetrics',
    'AlertRule',
    'Alert',
    'MetricCollector',
    'SystemMonitor',
    'PerformanceMonitor',
    'monitor_performance',
    'time_it',
    'get_performance_monitor',
    'record_metric',
    'get_health_status',
    
    # 任务调度
    'TaskStatus',
    'TaskPriority',
    'ScheduleType',
    'TaskResult',
    'Task',
    'TaskQueue',
    'TaskExecutor',
    'CronParser',
    'TaskScheduler',
    'scheduled_task',
    'async_task',
    'get_task_scheduler',
    'add_task',
    'get_task_status',
    
    # API网关
    'HTTPMethod',
    'AuthType',
    'RateLimitType',
    'ResponseFormat',
    'APIRequest',
    'APIResponse',
    'RateLimitRule',
    'APIEndpoint',
    'RateLimiter',
    'AuthManager',
    'RequestLogger',
    'APIGateway',
    'api_endpoint',
    'require_auth',
    'rate_limit',
    'get_api_gateway',
    
    # 数据库管理
    'DatabaseType',
    'TransactionIsolation',
    'QueryType',
    'FieldType',
    'DatabaseConfig',
    'QueryResult',
    'Field',
    'Table',
    'DatabaseConnection',
    'SQLiteConnection',
    'MemoryConnection',
    'ConnectionPool',
    'QueryBuilder',
    'DatabaseManager',
    'get_database_manager',
    'execute_query',
    'query_builder',
    'transaction',
    
    # 配置管理器
    'ConfigFormat',
    'ConfigSource',
    'ValidationLevel',
    'ConfigSchema',
    'ConfigMetadata',
    'ConfigChangeEvent',
    'ConfigLoader',
    'ConfigValidator',
    'ConfigWatcher',
    'ConfigManager',
    'get_config_manager',
    'load_config',
    'get_config',
    'get_config_value',
    'set_config_value',
    'config_required'
]
