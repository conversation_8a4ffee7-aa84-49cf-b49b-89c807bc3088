"""统一数据处理系统

整合项目中的数据处理功能，提供标准化的数据处理接口。
"""

import pandas as pd
import numpy as np
from typing import Any, Dict, List, Optional, Union, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import json
from pathlib import Path
from datetime import datetime, timedelta

from .interfaces import IDataProcessor, IDataValidator, ValidationResult
from .exceptions import DataException, ErrorCode
from .logging_manager import get_logger, log_performance


class DataType(Enum):
    """数据类型"""

    LOTTERY_NUMBERS = "lottery_numbers"
    HISTORICAL_DATA = "historical_data"
    PREDICTION_RESULTS = "prediction_results"
    FEATURES = "features"
    STATISTICS = "statistics"
    METADATA = "metadata"


class ProcessingStage(Enum):
    """处理阶段"""

    RAW = "raw"
    CLEANED = "cleaned"
    VALIDATED = "validated"
    TRANSFORMED = "transformed"
    ENRICHED = "enriched"
    READY = "ready"


@dataclass
class DataSchema:
    """数据模式定义"""

    name: str
    data_type: DataType
    required_columns: List[str] = field(default_factory=list)
    optional_columns: List[str] = field(default_factory=list)
    column_types: Dict[str, type] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    validation_rules: List[Callable] = field(default_factory=list)


@dataclass
class ProcessingStep:
    """处理步骤定义"""

    name: str
    processor: Callable
    stage: ProcessingStage
    required_stages: List[ProcessingStage] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True


@dataclass
class DataQualityMetrics:
    """数据质量指标"""

    completeness: float = 0.0  # 完整性
    accuracy: float = 0.0  # 准确性
    consistency: float = 0.0  # 一致性
    validity: float = 0.0  # 有效性
    uniqueness: float = 0.0  # 唯一性
    timeliness: float = 0.0  # 时效性
    overall_score: float = 0.0  # 总体评分
    issues: List[str] = field(default_factory=list)


class BaseDataProcessor(ABC):
    """基础数据处理器"""

    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(f"DataProcessor.{name}")

    @abstractmethod
    def process(self, data: Any, **kwargs) -> Any:
        """处理数据

        Args:
            data: 输入数据
            **kwargs: 处理参数

        Returns:
            Any: 处理后的数据
        """
        pass

    def validate_input(self, data: Any) -> bool:
        """验证输入数据

        Args:
            data: 输入数据

        Returns:
            bool: 是否有效
        """
        return data is not None


class LotteryDataCleaner(BaseDataProcessor):
    """彩票数据清洗器"""

    def __init__(self):
        super().__init__("LotteryDataCleaner")

    @log_performance
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """清洗彩票数据

        Args:
            data: 原始彩票数据
            **kwargs: 清洗参数

        Returns:
            pd.DataFrame: 清洗后的数据
        """
        if not isinstance(data, pd.DataFrame):
            raise DataException("输入数据必须是DataFrame", ErrorCode.DATA_TYPE_ERROR)

        cleaned_data = data.copy()

        # 移除重复行
        initial_count = len(cleaned_data)
        cleaned_data = cleaned_data.drop_duplicates()
        removed_duplicates = initial_count - len(cleaned_data)

        if removed_duplicates > 0:
            self.logger.info(f"移除重复行: {removed_duplicates}")

        # 处理缺失值
        missing_before = cleaned_data.isnull().sum().sum()

        # 根据列类型处理缺失值
        for column in cleaned_data.columns:
            if cleaned_data[column].dtype in ["int64", "float64"]:
                # 数值列用中位数填充
                cleaned_data[column].fillna(cleaned_data[column].median(), inplace=True)
            else:
                # 非数值列用众数填充
                mode_value = cleaned_data[column].mode()
                if not mode_value.empty:
                    cleaned_data[column].fillna(mode_value[0], inplace=True)

        missing_after = cleaned_data.isnull().sum().sum()

        if missing_before > missing_after:
            self.logger.info(f"处理缺失值: {missing_before} -> {missing_after}")

        # 数据类型转换
        number_columns = [
            col
            for col in cleaned_data.columns
            if "number" in col.lower() or "ball" in col.lower()
        ]
        for col in number_columns:
            try:
                cleaned_data[col] = pd.to_numeric(cleaned_data[col], errors="coerce")
            except Exception as e:
                self.logger.warning(f"转换列 {col} 类型失败: {e}")

        # 移除异常值
        cleaned_data = self._remove_outliers(cleaned_data, number_columns)

        self.logger.info(f"数据清洗完成: {initial_count} -> {len(cleaned_data)}")
        return cleaned_data

    def _remove_outliers(self, data: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """移除异常值

        Args:
            data: 数据
            columns: 需要检查的列

        Returns:
            pd.DataFrame: 移除异常值后的数据
        """
        cleaned_data = data.copy()

        for col in columns:
            if col in cleaned_data.columns and cleaned_data[col].dtype in [
                "int64",
                "float64",
            ]:
                Q1 = cleaned_data[col].quantile(0.25)
                Q3 = cleaned_data[col].quantile(0.75)
                IQR = Q3 - Q1

                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers_mask = (cleaned_data[col] < lower_bound) | (
                    cleaned_data[col] > upper_bound
                )
                outliers_count = outliers_mask.sum()

                if outliers_count > 0:
                    cleaned_data = cleaned_data[~outliers_mask]
                    self.logger.debug(f"列 {col} 移除异常值: {outliers_count}")

        return cleaned_data


class LotteryDataValidator(BaseDataProcessor, IDataValidator):
    """彩票数据验证器"""

    def __init__(self):
        super().__init__("LotteryDataValidator")
        self.schemas: Dict[str, DataSchema] = {}
        self._setup_default_schemas()

    def _setup_default_schemas(self):
        """设置默认数据模式"""
        # 双色球数据模式
        self.schemas["ssq"] = DataSchema(
            name="双色球",
            data_type=DataType.LOTTERY_NUMBERS,
            required_columns=[
                "期号",
                "红球1",
                "红球2",
                "红球3",
                "红球4",
                "红球5",
                "红球6",
                "蓝球",
            ],
            column_types={
                "期号": str,
                "红球1": int,
                "红球2": int,
                "红球3": int,
                "红球4": int,
                "红球5": int,
                "红球6": int,
                "蓝球": int,
            },
            constraints={"红球范围": (1, 33), "蓝球范围": (1, 16)},
        )

        # 大乐透数据模式
        self.schemas["dlt"] = DataSchema(
            name="大乐透",
            data_type=DataType.LOTTERY_NUMBERS,
            required_columns=[
                "期号",
                "前区1",
                "前区2",
                "前区3",
                "前区4",
                "前区5",
                "后区1",
                "后区2",
            ],
            column_types={
                "期号": str,
                "前区1": int,
                "前区2": int,
                "前区3": int,
                "前区4": int,
                "前区5": int,
                "后区1": int,
                "后区2": int,
            },
            constraints={"前区范围": (1, 35), "后区范围": (1, 12)},
        )

    def process(
        self, data: pd.DataFrame, schema_name: str = "ssq", **kwargs
    ) -> pd.DataFrame:
        """验证数据

        Args:
            data: 输入数据
            schema_name: 数据模式名称
            **kwargs: 验证参数

        Returns:
            pd.DataFrame: 验证后的数据
        """
        validation_result = self.validate(data, schema_name)

        if not validation_result.is_valid:
            raise DataException(
                f"数据验证失败: {', '.join(validation_result.errors)}",
                ErrorCode.DATA_VALIDATION_ERROR,
            )

        return data

    def validate(self, data: Any, schema_name: str = "ssq") -> ValidationResult:
        """验证数据

        Args:
            data: 待验证数据
            schema_name: 数据模式名称

        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        warnings = []

        try:
            if not isinstance(data, pd.DataFrame):
                errors.append("数据必须是DataFrame格式")
                return ValidationResult(False, errors, warnings)

            if schema_name not in self.schemas:
                errors.append(f"未知的数据模式: {schema_name}")
                return ValidationResult(False, errors, warnings)

            schema = self.schemas[schema_name]

            # 检查必需列
            missing_columns = set(schema.required_columns) - set(data.columns)
            if missing_columns:
                errors.append(f"缺少必需列: {missing_columns}")

            # 检查数据类型
            for column, expected_type in schema.column_types.items():
                if column in data.columns:
                    try:
                        if expected_type == int:
                            pd.to_numeric(data[column], errors="raise")
                        elif expected_type == str:
                            data[column].astype(str)
                    except Exception:
                        errors.append(
                            f"列 {column} 数据类型错误，期望 {expected_type.__name__}"
                        )

            # 检查约束条件
            if schema_name == "ssq":
                errors.extend(self._validate_ssq_constraints(data))
            elif schema_name == "dlt":
                errors.extend(self._validate_dlt_constraints(data))

            # 检查数据质量
            quality_issues = self._check_data_quality(data)
            warnings.extend(quality_issues)

        except Exception as e:
            errors.append(f"验证过程中发生异常: {e}")

        is_valid = len(errors) == 0
        return ValidationResult(is_valid, errors, warnings)

    def _validate_ssq_constraints(self, data: pd.DataFrame) -> List[str]:
        """验证双色球约束条件"""
        errors = []

        red_ball_columns = ["红球1", "红球2", "红球3", "红球4", "红球5", "红球6"]
        blue_ball_column = "蓝球"

        for idx, row in data.iterrows():
            # 检查红球范围
            for col in red_ball_columns:
                if col in row:
                    value = row[col]
                    if not (1 <= value <= 33):
                        errors.append(f"第{idx+1}行红球 {col} 超出范围: {value}")

            # 检查蓝球范围
            if blue_ball_column in row:
                value = row[blue_ball_column]
                if not (1 <= value <= 16):
                    errors.append(f"第{idx+1}行蓝球超出范围: {value}")

            # 检查红球是否重复
            red_balls = [row[col] for col in red_ball_columns if col in row]
            if len(red_balls) != len(set(red_balls)):
                errors.append(f"第{idx+1}行红球存在重复")

            # 检查红球是否按顺序排列
            if red_balls != sorted(red_balls):
                errors.append(f"第{idx+1}行红球未按升序排列")

        return errors

    def _validate_dlt_constraints(self, data: pd.DataFrame) -> List[str]:
        """验证大乐透约束条件"""
        errors = []

        front_columns = ["前区1", "前区2", "前区3", "前区4", "前区5"]
        back_columns = ["后区1", "后区2"]

        for idx, row in data.iterrows():
            # 检查前区范围
            for col in front_columns:
                if col in row:
                    value = row[col]
                    if not (1 <= value <= 35):
                        errors.append(f"第{idx+1}行前区 {col} 超出范围: {value}")

            # 检查后区范围
            for col in back_columns:
                if col in row:
                    value = row[col]
                    if not (1 <= value <= 12):
                        errors.append(f"第{idx+1}行后区 {col} 超出范围: {value}")

            # 检查前区是否重复
            front_balls = [row[col] for col in front_columns if col in row]
            if len(front_balls) != len(set(front_balls)):
                errors.append(f"第{idx+1}行前区存在重复")

            # 检查后区是否重复
            back_balls = [row[col] for col in back_columns if col in row]
            if len(back_balls) != len(set(back_balls)):
                errors.append(f"第{idx+1}行后区存在重复")

        return errors

    def _check_data_quality(self, data: pd.DataFrame) -> List[str]:
        """检查数据质量"""
        warnings = []

        # 检查缺失值
        missing_count = data.isnull().sum().sum()
        if missing_count > 0:
            warnings.append(f"存在 {missing_count} 个缺失值")

        # 检查重复行
        duplicate_count = data.duplicated().sum()
        if duplicate_count > 0:
            warnings.append(f"存在 {duplicate_count} 行重复数据")

        # 检查数据量
        if len(data) < 10:
            warnings.append("数据量过少，可能影响分析质量")

        return warnings


class LotteryDataTransformer(BaseDataProcessor):
    """彩票数据转换器"""

    def __init__(self):
        super().__init__("LotteryDataTransformer")

    @log_performance
    def process(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """转换数据

        Args:
            data: 输入数据
            **kwargs: 转换参数

        Returns:
            pd.DataFrame: 转换后的数据
        """
        transformed_data = data.copy()

        # 添加时间特征
        transformed_data = self._add_time_features(transformed_data)

        # 添加统计特征
        transformed_data = self._add_statistical_features(transformed_data)

        # 添加趋势特征
        transformed_data = self._add_trend_features(transformed_data)

        # 添加周期特征
        transformed_data = self._add_cycle_features(transformed_data)

        self.logger.info(
            f"数据转换完成，新增特征: {len(transformed_data.columns) - len(data.columns)}"
        )
        return transformed_data

    def _add_time_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加时间特征"""
        if "期号" in data.columns:
            # 提取年份、月份等时间信息
            try:
                # 假设期号格式为 YYYYNNN
                data["年份"] = data["期号"].str[:4].astype(int)
                data["期数"] = data["期号"].str[4:].astype(int)

                # 添加季度信息
                data["季度"] = ((data["期数"] - 1) // 26) + 1

                # 添加月份信息（假设每月约4期）
                data["月份"] = ((data["期数"] - 1) // 4) + 1

            except Exception as e:
                self.logger.warning(f"添加时间特征失败: {e}")

        return data

    def _add_statistical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加统计特征"""
        number_columns = [
            col
            for col in data.columns
            if any(x in col for x in ["红球", "蓝球", "前区", "后区"])
        ]

        if number_columns:
            # 计算和值
            data["和值"] = data[number_columns].sum(axis=1)

            # 计算平均值
            data["平均值"] = data[number_columns].mean(axis=1)

            # 计算标准差
            data["标准差"] = data[number_columns].std(axis=1)

            # 计算奇偶比
            odd_count = (data[number_columns] % 2 == 1).sum(axis=1)
            data["奇数个数"] = odd_count
            data["偶数个数"] = len(number_columns) - odd_count
            data["奇偶比"] = odd_count / len(number_columns)

            # 计算大小比
            mid_value = data[number_columns].max().max() // 2
            big_count = (data[number_columns] > mid_value).sum(axis=1)
            data["大数个数"] = big_count
            data["小数个数"] = len(number_columns) - big_count
            data["大小比"] = big_count / len(number_columns)

        return data

    def _add_trend_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加趋势特征"""
        if len(data) > 1:
            # 计算移动平均
            for window in [3, 5, 10]:
                if len(data) >= window:
                    data[f"和值_MA{window}"] = (
                        data["和值"].rolling(window=window).mean()
                    )
                    data[f"奇偶比_MA{window}"] = (
                        data["奇偶比"].rolling(window=window).mean()
                    )

            # 计算变化率
            data["和值变化率"] = data["和值"].pct_change()
            data["奇偶比变化率"] = data["奇偶比"].pct_change()

        return data

    def _add_cycle_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加周期特征"""
        if "期数" in data.columns:
            # 添加周期性特征
            data["期数_sin"] = np.sin(2 * np.pi * data["期数"] / 52)  # 年周期
            data["期数_cos"] = np.cos(2 * np.pi * data["期数"] / 52)

            # 添加季度周期
            data["季度_sin"] = np.sin(2 * np.pi * data["期数"] / 13)  # 季度周期
            data["季度_cos"] = np.cos(2 * np.pi * data["期数"] / 13)

        return data


class UnifiedDataProcessor(IDataProcessor):
    """统一数据处理器"""

    def __init__(self):
        self.logger = get_logger("UnifiedDataProcessor")
        self.processors: Dict[ProcessingStage, List[BaseDataProcessor]] = {
            ProcessingStage.CLEANED: [LotteryDataCleaner()],
            ProcessingStage.VALIDATED: [LotteryDataValidator()],
            ProcessingStage.TRANSFORMED: [LotteryDataTransformer()],
        }
        self.processing_steps: List[ProcessingStep] = []
        self.current_stage = ProcessingStage.RAW
        self.quality_metrics: Optional[DataQualityMetrics] = None

    def add_processor(self, stage: ProcessingStage, processor: BaseDataProcessor):
        """添加处理器

        Args:
            stage: 处理阶段
            processor: 处理器
        """
        if stage not in self.processors:
            self.processors[stage] = []
        self.processors[stage].append(processor)

    def add_processing_step(self, step: ProcessingStep):
        """添加处理步骤

        Args:
            step: 处理步骤
        """
        self.processing_steps.append(step)

    @log_performance
    def process(
        self, data: Any, target_stage: ProcessingStage = ProcessingStage.READY, **kwargs
    ) -> Any:
        """处理数据

        Args:
            data: 输入数据
            target_stage: 目标处理阶段
            **kwargs: 处理参数

        Returns:
            Any: 处理后的数据
        """
        current_data = data
        self.current_stage = ProcessingStage.RAW

        # 定义处理顺序
        stage_order = [
            ProcessingStage.CLEANED,
            ProcessingStage.VALIDATED,
            ProcessingStage.TRANSFORMED,
            ProcessingStage.ENRICHED,
            ProcessingStage.READY,
        ]

        target_index = (
            stage_order.index(target_stage)
            if target_stage in stage_order
            else len(stage_order)
        )

        for i, stage in enumerate(stage_order[: target_index + 1]):
            if stage in self.processors:
                for processor in self.processors[stage]:
                    try:
                        self.logger.debug(f"执行处理器: {processor.name}")
                        current_data = processor.process(current_data, **kwargs)
                        self.current_stage = stage
                    except Exception as e:
                        self.logger.error(f"处理器 {processor.name} 执行失败: {e}")
                        raise DataException(
                            f"数据处理失败: {processor.name}",
                            ErrorCode.DATA_PROCESSING_ERROR,
                            cause=e,
                        )

        # 计算数据质量指标
        if isinstance(current_data, pd.DataFrame):
            self.quality_metrics = self._calculate_quality_metrics(current_data)

        self.logger.info(f"数据处理完成，当前阶段: {self.current_stage.value}")
        return current_data

    def validate(self, data: Any, **kwargs) -> ValidationResult:
        """验证数据

        Args:
            data: 待验证数据
            **kwargs: 验证参数

        Returns:
            ValidationResult: 验证结果
        """
        if ProcessingStage.VALIDATED in self.processors:
            for processor in self.processors[ProcessingStage.VALIDATED]:
                if isinstance(processor, IDataValidator):
                    return processor.validate(data, **kwargs)

        # 默认验证
        return ValidationResult(True, [], [])

    def get_quality_metrics(self) -> Optional[DataQualityMetrics]:
        """获取数据质量指标

        Returns:
            Optional[DataQualityMetrics]: 质量指标
        """
        return self.quality_metrics

    def _calculate_quality_metrics(self, data: pd.DataFrame) -> DataQualityMetrics:
        """计算数据质量指标

        Args:
            data: 数据

        Returns:
            DataQualityMetrics: 质量指标
        """
        total_cells = data.size

        # 完整性：非空值比例
        completeness = (
            (total_cells - data.isnull().sum().sum()) / total_cells
            if total_cells > 0
            else 0
        )

        # 唯一性：非重复行比例
        uniqueness = (
            (len(data) - data.duplicated().sum()) / len(data) if len(data) > 0 else 0
        )

        # 一致性：数据类型一致性
        consistency = self._calculate_consistency(data)

        # 有效性：数据范围有效性
        validity = self._calculate_validity(data)

        # 准确性：基于业务规则的准确性
        accuracy = self._calculate_accuracy(data)

        # 时效性：数据新鲜度
        timeliness = self._calculate_timeliness(data)

        # 总体评分
        overall_score = (
            completeness + uniqueness + consistency + validity + accuracy + timeliness
        ) / 6

        return DataQualityMetrics(
            completeness=completeness,
            accuracy=accuracy,
            consistency=consistency,
            validity=validity,
            uniqueness=uniqueness,
            timeliness=timeliness,
            overall_score=overall_score,
        )

    def _calculate_consistency(self, data: pd.DataFrame) -> float:
        """计算一致性"""
        # 检查数据类型一致性
        consistency_score = 1.0

        for column in data.columns:
            if data[column].dtype == "object":
                # 检查字符串列的格式一致性
                unique_patterns = data[column].astype(str).str.len().nunique()
                if unique_patterns > len(data) * 0.1:  # 如果格式变化太多
                    consistency_score -= 0.1

        return max(0.0, consistency_score)

    def _calculate_validity(self, data: pd.DataFrame) -> float:
        """计算有效性"""
        validity_score = 1.0

        # 检查数值列的范围有效性
        number_columns = data.select_dtypes(include=[np.number]).columns

        for column in number_columns:
            if "ball" in column.lower() or "球" in column:
                # 彩票号码有效性检查
                if "红" in column or "red" in column.lower():
                    invalid_count = ((data[column] < 1) | (data[column] > 33)).sum()
                elif "蓝" in column or "blue" in column.lower():
                    invalid_count = ((data[column] < 1) | (data[column] > 16)).sum()
                elif "前区" in column:
                    invalid_count = ((data[column] < 1) | (data[column] > 35)).sum()
                elif "后区" in column:
                    invalid_count = ((data[column] < 1) | (data[column] > 12)).sum()
                else:
                    continue

                if len(data) > 0:
                    validity_score -= (invalid_count / len(data)) * 0.2

        return max(0.0, validity_score)

    def _calculate_accuracy(self, data: pd.DataFrame) -> float:
        """计算准确性"""
        # 基于业务规则的准确性检查
        accuracy_score = 1.0

        # 检查彩票号码的业务规则
        red_columns = [col for col in data.columns if "红球" in col]
        if len(red_columns) >= 6:
            # 检查红球是否按升序排列
            for idx, row in data.iterrows():
                red_balls = [row[col] for col in red_columns[:6]]
                if red_balls != sorted(red_balls):
                    accuracy_score -= 0.01

        return max(0.0, accuracy_score)

    def _calculate_timeliness(self, data: pd.DataFrame) -> float:
        """计算时效性"""
        # 基于数据的时间戳或期号计算时效性
        if "期号" in data.columns:
            try:
                # 假设期号包含年份信息
                latest_year = data["期号"].str[:4].astype(int).max()
                current_year = datetime.now().year

                if latest_year >= current_year:
                    return 1.0
                elif latest_year >= current_year - 1:
                    return 0.8
                elif latest_year >= current_year - 2:
                    return 0.6
                else:
                    return 0.4
            except Exception:
                pass

        return 0.5  # 默认中等时效性


# 全局数据处理器实例
_data_processor = None


def get_data_processor() -> UnifiedDataProcessor:
    """获取数据处理器实例

    Returns:
        UnifiedDataProcessor: 数据处理器实例
    """
    global _data_processor
    if _data_processor is None:
        _data_processor = UnifiedDataProcessor()
    return _data_processor


# 导出的公共接口
__all__ = [
    "DataType",
    "ProcessingStage",
    "DataSchema",
    "ProcessingStep",
    "DataQualityMetrics",
    "BaseDataProcessor",
    "LotteryDataCleaner",
    "LotteryDataValidator",
    "LotteryDataTransformer",
    "UnifiedDataProcessor",
    "get_data_processor",
]
