"""统一性能监控系统

提供应用性能监控、指标收集和分析功能。
"""

import time
import psutil
import threading
import statistics
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from collections import defaultdict, deque
from functools import wraps
import json
from pathlib import Path

from .exceptions import SystemException, ErrorCode
from .logging_manager import get_logger


class MetricType(Enum):
    """指标类型"""
    COUNTER = 'counter'  # 计数器
    GAUGE = 'gauge'  # 仪表
    HISTOGRAM = 'histogram'  # 直方图
    TIMER = 'timer'  # 计时器
    RATE = 'rate'  # 速率


class AlertLevel(Enum):
    """告警级别"""
    INFO = 'info'
    WARNING = 'warning'
    ERROR = 'error'
    CRITICAL = 'critical'


@dataclass
class MetricValue:
    """指标值"""
    value: Union[int, float]
    timestamp: float = field(default_factory=time.time)
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    # 系统指标
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used: int = 0
    memory_available: int = 0
    disk_usage_percent: float = 0.0
    
    # 应用指标
    request_count: int = 0
    error_count: int = 0
    response_time_avg: float = 0.0
    response_time_p95: float = 0.0
    response_time_p99: float = 0.0
    
    # 自定义指标
    custom_metrics: Dict[str, float] = field(default_factory=dict)
    
    # 时间戳
    timestamp: float = field(default_factory=time.time)


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric_name: str
    condition: str  # 条件表达式，如 '> 80', '< 10'
    level: AlertLevel
    message: str
    enabled: bool = True
    cooldown: int = 300  # 冷却时间（秒）
    last_triggered: Optional[float] = None
    
    def should_trigger(self, value: float) -> bool:
        """检查是否应该触发告警"""
        if not self.enabled:
            return False
        
        # 检查冷却时间
        if self.last_triggered and time.time() - self.last_triggered < self.cooldown:
            return False
        
        # 评估条件
        try:
            return eval(f"{value} {self.condition}")
        except Exception:
            return False


@dataclass
class Alert:
    """告警"""
    rule_name: str
    metric_name: str
    value: float
    level: AlertLevel
    message: str
    timestamp: float = field(default_factory=time.time)
    resolved: bool = False
    resolved_at: Optional[float] = None


class MetricCollector:
    """指标收集器"""
    
    def __init__(self, name: str, metric_type: MetricType, max_size: int = 1000):
        self.name = name
        self.metric_type = metric_type
        self.max_size = max_size
        self.values = deque(maxlen=max_size)
        self._lock = threading.RLock()
    
    def add_value(self, value: Union[int, float], tags: Optional[Dict[str, str]] = None):
        """添加指标值"""
        with self._lock:
            metric_value = MetricValue(
                value=value,
                tags=tags or {},
                timestamp=time.time()
            )
            self.values.append(metric_value)
    
    def get_latest(self) -> Optional[MetricValue]:
        """获取最新值"""
        with self._lock:
            return self.values[-1] if self.values else None
    
    def get_values(self, since: Optional[float] = None) -> List[MetricValue]:
        """获取指标值列表"""
        with self._lock:
            if since is None:
                return list(self.values)
            return [v for v in self.values if v.timestamp >= since]
    
    def get_statistics(self, since: Optional[float] = None) -> Dict[str, float]:
        """获取统计信息"""
        values = [v.value for v in self.get_values(since)]
        
        if not values:
            return {}
        
        return {
            'count': len(values),
            'sum': sum(values),
            'avg': statistics.mean(values),
            'min': min(values),
            'max': max(values),
            'median': statistics.median(values),
            'p95': self._percentile(values, 0.95),
            'p99': self._percentile(values, 0.99)
        }
    
    def _percentile(self, values: List[float], p: float) -> float:
        """计算百分位数"""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int(len(sorted_values) * p)
        return sorted_values[min(index, len(sorted_values) - 1)]
    
    def clear(self):
        """清空指标"""
        with self._lock:
            self.values.clear()


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.logger = get_logger('SystemMonitor')
    
    def get_cpu_percent(self) -> float:
        """获取CPU使用率"""
        try:
            return psutil.cpu_percent(interval=1)
        except Exception as e:
            self.logger.error(f"获取CPU使用率失败: {e}")
            return 0.0
    
    def get_memory_info(self) -> Dict[str, Union[int, float]]:
        """获取内存信息"""
        try:
            memory = psutil.virtual_memory()
            return {
                'total': memory.total,
                'available': memory.available,
                'used': memory.used,
                'percent': memory.percent
            }
        except Exception as e:
            self.logger.error(f"获取内存信息失败: {e}")
            return {'total': 0, 'available': 0, 'used': 0, 'percent': 0.0}
    
    def get_disk_usage(self, path: str = '/') -> Dict[str, Union[int, float]]:
        """获取磁盘使用情况"""
        try:
            usage = psutil.disk_usage(path)
            return {
                'total': usage.total,
                'used': usage.used,
                'free': usage.free,
                'percent': (usage.used / usage.total) * 100
            }
        except Exception as e:
            self.logger.error(f"获取磁盘使用情况失败: {e}")
            return {'total': 0, 'used': 0, 'free': 0, 'percent': 0.0}
    
    def get_network_io(self) -> Dict[str, int]:
        """获取网络IO统计"""
        try:
            net_io = psutil.net_io_counters()
            return {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            }
        except Exception as e:
            self.logger.error(f"获取网络IO统计失败: {e}")
            return {'bytes_sent': 0, 'bytes_recv': 0, 'packets_sent': 0, 'packets_recv': 0}
    
    def get_process_info(self, pid: Optional[int] = None) -> Dict[str, Any]:
        """获取进程信息"""
        try:
            process = psutil.Process(pid) if pid else psutil.Process()
            return {
                'pid': process.pid,
                'name': process.name(),
                'cpu_percent': process.cpu_percent(),
                'memory_percent': process.memory_percent(),
                'memory_info': process.memory_info()._asdict(),
                'create_time': process.create_time(),
                'status': process.status()
            }
        except Exception as e:
            self.logger.error(f"获取进程信息失败: {e}")
            return {}


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, collection_interval: int = 60):
        self.logger = get_logger('PerformanceMonitor')
        self.collection_interval = collection_interval
        
        # 指标收集器
        self.collectors: Dict[str, MetricCollector] = {}
        
        # 系统监控器
        self.system_monitor = SystemMonitor()
        
        # 告警规则和历史
        self.alert_rules: Dict[str, AlertRule] = {}
        self.alert_history: List[Alert] = []
        
        # 监控线程
        self._monitor_thread = None
        self._stop_monitoring = threading.Event()
        
        # 锁
        self._lock = threading.RLock()
        
        # 初始化默认指标收集器
        self._init_default_collectors()
        
        # 启动监控
        self.start_monitoring()
    
    def _init_default_collectors(self):
        """初始化默认指标收集器"""
        default_metrics = [
            ('cpu_percent', MetricType.GAUGE),
            ('memory_percent', MetricType.GAUGE),
            ('disk_usage_percent', MetricType.GAUGE),
            ('request_count', MetricType.COUNTER),
            ('error_count', MetricType.COUNTER),
            ('response_time', MetricType.TIMER)
        ]
        
        for name, metric_type in default_metrics:
            self.collectors[name] = MetricCollector(name, metric_type)
    
    def add_collector(self, name: str, metric_type: MetricType, max_size: int = 1000):
        """添加指标收集器"""
        with self._lock:
            self.collectors[name] = MetricCollector(name, metric_type, max_size)
    
    def record_metric(self, name: str, value: Union[int, float], tags: Optional[Dict[str, str]] = None):
        """记录指标"""
        with self._lock:
            if name not in self.collectors:
                # 自动创建收集器
                self.collectors[name] = MetricCollector(name, MetricType.GAUGE)
            
            self.collectors[name].add_value(value, tags)
            
            # 检查告警
            self._check_alerts(name, value)
    
    def get_metric(self, name: str) -> Optional[MetricValue]:
        """获取最新指标值"""
        with self._lock:
            collector = self.collectors.get(name)
            return collector.get_latest() if collector else None
    
    def get_metric_statistics(self, name: str, since: Optional[float] = None) -> Dict[str, float]:
        """获取指标统计信息"""
        with self._lock:
            collector = self.collectors.get(name)
            return collector.get_statistics(since) if collector else {}
    
    def get_all_metrics(self) -> PerformanceMetrics:
        """获取所有性能指标"""
        with self._lock:
            # 获取系统指标
            cpu_percent = self.get_metric('cpu_percent')
            memory_percent = self.get_metric('memory_percent')
            disk_usage_percent = self.get_metric('disk_usage_percent')
            
            # 获取应用指标
            request_stats = self.get_metric_statistics('request_count', time.time() - 3600)
            error_stats = self.get_metric_statistics('error_count', time.time() - 3600)
            response_time_stats = self.get_metric_statistics('response_time', time.time() - 3600)
            
            # 获取自定义指标
            custom_metrics = {}
            for name, collector in self.collectors.items():
                if name not in ['cpu_percent', 'memory_percent', 'disk_usage_percent', 
                               'request_count', 'error_count', 'response_time']:
                    latest = collector.get_latest()
                    if latest:
                        custom_metrics[name] = latest.value
            
            return PerformanceMetrics(
                cpu_percent=cpu_percent.value if cpu_percent else 0.0,
                memory_percent=memory_percent.value if memory_percent else 0.0,
                disk_usage_percent=disk_usage_percent.value if disk_usage_percent else 0.0,
                request_count=int(request_stats.get('count', 0)),
                error_count=int(error_stats.get('count', 0)),
                response_time_avg=response_time_stats.get('avg', 0.0),
                response_time_p95=response_time_stats.get('p95', 0.0),
                response_time_p99=response_time_stats.get('p99', 0.0),
                custom_metrics=custom_metrics
            )
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则"""
        with self._lock:
            self.alert_rules[rule.name] = rule
    
    def remove_alert_rule(self, name: str):
        """移除告警规则"""
        with self._lock:
            self.alert_rules.pop(name, None)
    
    def _check_alerts(self, metric_name: str, value: float):
        """检查告警"""
        for rule_name, rule in self.alert_rules.items():
            if rule.metric_name == metric_name and rule.should_trigger(value):
                # 触发告警
                alert = Alert(
                    rule_name=rule_name,
                    metric_name=metric_name,
                    value=value,
                    level=rule.level,
                    message=rule.message.format(value=value)
                )
                
                self.alert_history.append(alert)
                rule.last_triggered = time.time()
                
                # 记录告警日志
                log_method = getattr(self.logger, rule.level.value)
                log_method(f"告警触发: {alert.message}")
    
    def get_alerts(self, since: Optional[float] = None, resolved: Optional[bool] = None) -> List[Alert]:
        """获取告警历史"""
        with self._lock:
            alerts = self.alert_history
            
            if since is not None:
                alerts = [a for a in alerts if a.timestamp >= since]
            
            if resolved is not None:
                alerts = [a for a in alerts if a.resolved == resolved]
            
            return alerts
    
    def start_monitoring(self):
        """启动监控"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            return
        
        def monitor_worker():
            while not self._stop_monitoring.wait(self.collection_interval):
                try:
                    self._collect_system_metrics()
                except Exception as e:
                    self.logger.error(f"收集系统指标失败: {e}")
        
        self._monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self._stop_monitoring.set()
        
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)
        
        self.logger.info("性能监控已停止")
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        # CPU使用率
        cpu_percent = self.system_monitor.get_cpu_percent()
        self.record_metric('cpu_percent', cpu_percent)
        
        # 内存使用率
        memory_info = self.system_monitor.get_memory_info()
        self.record_metric('memory_percent', memory_info['percent'])
        
        # 磁盘使用率
        disk_usage = self.system_monitor.get_disk_usage('.')
        self.record_metric('disk_usage_percent', disk_usage['percent'])
    
    def export_metrics(self, file_path: str, format: str = 'json'):
        """导出指标数据"""
        try:
            metrics_data = {
                'timestamp': time.time(),
                'metrics': {},
                'alerts': []
            }
            
            # 导出指标
            for name, collector in self.collectors.items():
                values = collector.get_values()
                metrics_data['metrics'][name] = {
                    'type': collector.metric_type.value,
                    'values': [{
                        'value': v.value,
                        'timestamp': v.timestamp,
                        'tags': v.tags
                    } for v in values],
                    'statistics': collector.get_statistics()
                }
            
            # 导出告警
            metrics_data['alerts'] = [{
                'rule_name': a.rule_name,
                'metric_name': a.metric_name,
                'value': a.value,
                'level': a.level.value,
                'message': a.message,
                'timestamp': a.timestamp,
                'resolved': a.resolved
            } for a in self.alert_history]
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                if format == 'json':
                    json.dump(metrics_data, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的格式: {format}")
            
            self.logger.info(f"指标数据已导出到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"导出指标数据失败: {e}")
            raise SystemException(f"导出指标数据失败: {e}", ErrorCode.FILE_WRITE_ERROR)
    
    def clear_metrics(self, metric_name: Optional[str] = None):
        """清空指标数据"""
        with self._lock:
            if metric_name:
                collector = self.collectors.get(metric_name)
                if collector:
                    collector.clear()
            else:
                for collector in self.collectors.values():
                    collector.clear()
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        metrics = self.get_all_metrics()
        
        # 计算健康分数
        health_score = 100
        issues = []
        
        # CPU检查
        if metrics.cpu_percent > 80:
            health_score -= 20
            issues.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        # 内存检查
        if metrics.memory_percent > 80:
            health_score -= 20
            issues.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        # 磁盘检查
        if metrics.disk_usage_percent > 90:
            health_score -= 15
            issues.append(f"磁盘使用率过高: {metrics.disk_usage_percent:.1f}%")
        
        # 错误率检查
        if metrics.request_count > 0:
            error_rate = (metrics.error_count / metrics.request_count) * 100
            if error_rate > 5:
                health_score -= 25
                issues.append(f"错误率过高: {error_rate:.1f}%")
        
        # 响应时间检查
        if metrics.response_time_p95 > 1000:  # 1秒
            health_score -= 10
            issues.append(f"响应时间过长: P95={metrics.response_time_p95:.0f}ms")
        
        # 确定状态
        if health_score >= 90:
            status = 'healthy'
        elif health_score >= 70:
            status = 'warning'
        elif health_score >= 50:
            status = 'degraded'
        else:
            status = 'unhealthy'
        
        return {
            'status': status,
            'score': max(0, health_score),
            'issues': issues,
            'metrics': metrics,
            'timestamp': time.time()
        }


def monitor_performance(metric_name: str = 'response_time'):
    """性能监控装饰器
    
    Args:
        metric_name: 指标名称
    
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                # 记录成功请求
                get_performance_monitor().record_metric('request_count', 1)
                return result
            
            except Exception as e:
                # 记录错误请求
                get_performance_monitor().record_metric('error_count', 1)
                raise
            
            finally:
                # 记录响应时间
                duration = (time.time() - start_time) * 1000  # 毫秒
                get_performance_monitor().record_metric(metric_name, duration)
        
        return wrapper
    return decorator


def time_it(metric_name: Optional[str] = None):
    """计时装饰器
    
    Args:
        metric_name: 指标名称，默认使用函数名
    
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                return func(*args, **kwargs)
            finally:
                duration = (time.time() - start_time) * 1000  # 毫秒
                name = metric_name or f"{func.__name__}_duration"
                get_performance_monitor().record_metric(name, duration)
        
        return wrapper
    return decorator


# 全局性能监控器实例
_performance_monitor = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例
    
    Returns:
        PerformanceMonitor: 性能监控器实例
    """
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def record_metric(name: str, value: Union[int, float], tags: Optional[Dict[str, str]] = None):
    """记录指标的便捷函数
    
    Args:
        name: 指标名称
        value: 指标值
        tags: 标签
    """
    get_performance_monitor().record_metric(name, value, tags)


def get_health_status() -> Dict[str, Any]:
    """获取健康状态的便捷函数
    
    Returns:
        Dict[str, Any]: 健康状态信息
    """
    return get_performance_monitor().get_health_status()


# 导出的公共接口
__all__ = [
    'MetricType',
    'AlertLevel',
    'MetricValue',
    'PerformanceMetrics',
    'AlertRule',
    'Alert',
    'MetricCollector',
    'SystemMonitor',
    'PerformanceMonitor',
    'monitor_performance',
    'time_it',
    'get_performance_monitor',
    'record_metric',
    'get_health_status'
]