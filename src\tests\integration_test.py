"""集成测试套件
测试重构后架构的完整功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import os

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core import (
    DependencyContainer, ConfigurationManager, ConfigurablePredictorFactory,
    BallType, PredictionType, StandardBasePredictor
)
from src.core.interfaces import IStandardPredictor, ValidationResult
from src.core.predictor_adapter import create_legacy_adapter
from src.core.exceptions import PredictionException, ValidationException
from src.systems.refactored_main import RefactoredLotterySystem


class IntegrationTestSuite:
    """集成测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        self.test_results = []
        self.sample_data = self._create_sample_data()
    
    def _create_sample_data(self):
        """创建测试数据"""
        np.random.seed(42)
        data = []
        for i in range(100):
            period = f"25{i:03d}"
            red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
            blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
            data.append({
                'period': period,
                'red_1': red_balls[0], 'red_2': red_balls[1], 'red_3': red_balls[2],
                'red_4': red_balls[3], 'red_5': red_balls[4],
                'blue_1': blue_balls[0], 'blue_2': blue_balls[1]
            })
        return pd.DataFrame(data)
    
    def _log_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "* PASS" if success else "* FAIL"
        result = {
            "test": test_name,
            "status": status,
            "message": message
        }
        self.test_results.append(result)
        print(f"{status}: {test_name} - {message}")
    
    def test_core_architecture_integration(self):
        """测试核心架构组件集成"""
        try:
            # 测试依赖注入容器
            container = DependencyContainer()
            config_manager = ConfigurationManager()
            
            # 注册服务
            container.register_singleton(ConfigurationManager, config_manager)
            
            # 解析服务
            resolved_config = container.resolve(ConfigurationManager)
            assert resolved_config is config_manager
            
            # 测试配置管理器
            config = config_manager.load_config()
            assert config is not None
            
            # 测试预测器工厂
            factory = ConfigurablePredictorFactory()
            available_predictors = factory.get_available_predictors()
            assert isinstance(available_predictors, list)
            
            self._log_test_result("核心架构集成", True, f"可用预测器: {len(available_predictors)}个")
            
        except Exception as e:
            self._log_test_result("核心架构集成", False, str(e))
    
    def test_refactored_system_initialization(self):
        """测试重构后系统初始化"""
        try:
            system = RefactoredLotterySystem()
            assert system is not None
            assert system.config is not None
            
            self._log_test_result("重构系统初始化", True, "系统初始化成功")
            
        except Exception as e:
            self._log_test_result("重构系统初始化", False, str(e))
    
    def test_legacy_predictor_adaptation(self):
        """测试旧预测器适配"""
        try:
            # 创建一个模拟的旧预测器
            class MockLegacyPredictor:
                def __init__(self):
                    self.config = {"test": True}
                    
                def predict(self, data, target_index, **kwargs):
                    return {"value": "1:4", "confidence": 0.8}
            
            # 创建适配器
            legacy_predictor = MockLegacyPredictor()
            adapter = create_legacy_adapter(legacy_predictor, "TestAdapter", "1.0")
            
            # 测试适配器接口
            assert isinstance(adapter, IStandardPredictor)
            
            # 测试模型信息
            model_info = adapter.get_model_info()
            assert model_info["name"] == "TestAdapter"
            assert model_info["is_adapted"] is True
            
            # 测试输入验证
            validation = adapter.validate_input(self.sample_data, 10)
            assert isinstance(validation, ValidationResult)
            assert validation.is_valid
            
            # 测试预测
            result = adapter.predict(self.sample_data, 10, ball_type=BallType.RED)
            assert result is not None
            assert result.value == "1:4"
            assert result.confidence == 0.8
            
            self._log_test_result("旧预测器适配", True, "适配器功能正常")
            
        except Exception as e:
            self._log_test_result("旧预测器适配", False, str(e))
    
    def test_standard_predictor_interface(self):
        """测试标准预测器接口"""
        try:
            class TestStandardPredictor(StandardBasePredictor):
                def __init__(self):
                    super().__init__("TestPredictor", PredictionType.NUMBERS)
                
                def predict(self, data, target_index, **kwargs):
                    from src.core.interfaces import PredictionResult as StandardPredictionResult
                    return StandardPredictionResult(
                        prediction_type=self.prediction_type,
                        ball_type=kwargs.get('ball_type', BallType.RED),
                        value=[1, 2, 3, 4, 5],
                        confidence=0.7,
                        metadata={"test": True}
                    )
            
            predictor = TestStandardPredictor()
            
            # 测试基础功能
            assert predictor.name == "TestPredictor"
            assert predictor.get_prediction_type() == PredictionType.NUMBERS
            
            # 测试输入验证
            validation = predictor.validate_input(self.sample_data, 10)
            assert validation.is_valid
            
            # 测试预测
            result = predictor.predict(self.sample_data, 10, ball_type=BallType.RED)
            assert result.value == [1, 2, 3, 4, 5]
            assert result.confidence == 0.7
            
            # 测试批量预测
            batch_results = predictor.predict_batch(self.sample_data, [10, 11, 12])
            assert len(batch_results) == 3
            
            self._log_test_result("标准预测器接口", True, "接口功能完整")
            
        except Exception as e:
            self._log_test_result("标准预测器接口", False, str(e))
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        try:
            class ErrorPredictor(StandardBasePredictor):
                def __init__(self):
                    super().__init__("ErrorPredictor", PredictionType.NUMBERS)
                
                def predict(self, data, target_index, **kwargs):
                    raise ValueError("测试错误")
            
            predictor = ErrorPredictor()
            
            # 测试批量预测的错误处理
            batch_results = predictor.predict_batch(self.sample_data, [10, 11])
            assert len(batch_results) == 2
            for result in batch_results:
                assert "error" in result.metadata
            
            self._log_test_result("错误处理集成", True, "错误处理机制正常")
            
        except Exception as e:
            self._log_test_result("错误处理集成", False, str(e))
    
    def test_configuration_integration(self):
        """测试配置集成"""
        try:
            from src.core.configuration_manager import JsonConfigurationSource
            
            config_manager = ConfigurationManager()
            
            # 测试添加配置源
            if Path("config/system.json").exists():
                config_manager.add_source(JsonConfigurationSource("config/system.json"))
                config = config_manager.load_config()
                assert config is not None
                self._log_test_result("配置集成", True, "配置文件加载成功")
            else:
                self._log_test_result("配置集成", True, "配置文件不存在，跳过测试")
                
        except Exception as e:
            self._log_test_result("配置集成", False, str(e))
    
    def test_predictor_factory_integration(self):
        """测试预测器工厂集成"""
        try:
            factory = ConfigurablePredictorFactory()
            
            # 测试获取可用预测器
            available = factory.get_available_predictors()
            assert isinstance(available, list)
            
            # 测试创建预测器（如果有可用的）
            created_count = 0
            for predictor_name in available[:3]:  # 只测试前3个
                try:
                    predictor = factory.create_predictor(predictor_name)
                    if predictor is not None:
                        created_count += 1
                except Exception:
                    pass  # 忽略创建失败的预测器
            
            self._log_test_result("预测器工厂集成", True, f"成功创建 {created_count}/{len(available)} 个预测器")
            
        except Exception as e:
            self._log_test_result("预测器工厂集成", False, str(e))
    
    def test_data_flow_integration(self):
        """测试数据流集成"""
        try:
            class SimplePredictor(StandardBasePredictor):
                def __init__(self):
                    super().__init__("SimplePredictor", PredictionType.NUMBERS)
                
                def predict(self, data, target_index, **kwargs):
                    from src.core.interfaces import PredictionResult as StandardPredictionResult
                    
                    # 简单的数据处理逻辑
                    if target_index > 0:
                        prev_row = data.iloc[target_index - 1]
                        red_sum = sum([prev_row[f'red_{i}'] for i in range(1, 6)])
                        predicted_numbers = [(red_sum + i) % 35 + 1 for i in range(5)]
                    else:
                        predicted_numbers = [1, 2, 3, 4, 5]
                    
                    return StandardPredictionResult(
                        prediction_type=self.prediction_type,
                        ball_type=kwargs.get('ball_type', BallType.RED),
                        value=predicted_numbers,
                        confidence=0.6,
                        metadata={"method": "simple_sum"}
                    )
            
            predictor = SimplePredictor()
            
            # 测试数据流
            result = predictor.predict(self.sample_data, 50, ball_type=BallType.RED)
            assert result is not None
            assert len(result.value) == 5
            assert all(1 <= num <= 35 for num in result.value)
            
            self._log_test_result("数据流集成", True, "数据流处理正常")
            
        except Exception as e:
            self._log_test_result("数据流集成", False, str(e))
    
    def test_full_system_integration(self):
        """测试完整系统集成"""
        try:
            # 初始化系统
            system = RefactoredLotterySystem()
            
            # 测试系统状态
            assert system.config is not None
            
            # 如果有数据文件，测试数据加载
            data_path = Path("data/dlt_history.csv")
            if data_path.exists():
                self._log_test_result("完整系统集成", True, "发现历史数据文件，系统完整")
            else:
                self._log_test_result("完整系统集成", True, "系统初始化正常，未发现数据文件")
            
        except Exception as e:
            self._log_test_result("完整系统集成", False, str(e))
    
    def run_all_tests(self):
        """运行所有集成测试"""
        print("* 开始运行集成测试...")
        print("=" * 60)
        
        # 运行各项测试
        test_methods = [
            ("核心架构集成", self.test_core_architecture_integration),
            ("重构系统初始化", self.test_refactored_system_initialization),
            ("旧预测器适配", self.test_legacy_predictor_adaptation),
            ("标准预测器接口", self.test_standard_predictor_interface),
            ("错误处理集成", self.test_error_handling_integration),
            ("配置集成", self.test_configuration_integration),
            ("预测器工厂集成", self.test_predictor_factory_integration),
            ("数据流集成", self.test_data_flow_integration),
            ("完整系统集成", self.test_full_system_integration),
        ]
        
        for test_name, test_method in test_methods:
            print(f"\n* 运行测试: {test_name}")
            test_method()
        
        # 输出测试总结
        self._print_test_summary()
    
    def _print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("* 测试结果总结")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if "*" in result["status"])
        failed = sum(1 for result in self.test_results if "*" in result["status"])
        total = len(self.test_results)
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if failed > 0:
            print("\n* 失败的测试:")
            for result in self.test_results:
                if "*" in result["status"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n" + "=" * 60)
        if failed == 0:
            print("* 所有集成测试通过！架构重构成功！")
        else:
            print(f"* {failed} 个测试失败，需要进一步修复")


def main():
    """主函数"""
    test_suite = IntegrationTestSuite()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()